/**
 * Simple PDF Download Functionality
 * 
 * This script provides a simplified approach to PDF generation for the sales report.
 */

// Wait for the page to fully load
window.addEventListener('load', function() {
    console.log('Simple PDF Download script loaded');
    
    // Get the download button
    const downloadBtn = document.getElementById('download-report-btn');
    
    if (downloadBtn) {
        // Add click event listener
        downloadBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Download PDF button clicked');
            
            try {
                // Check if html2canvas is available
                if (typeof html2canvas !== 'function') {
                    throw new Error('html2canvas library not loaded');
                }
                
                // Check if jsPDF is available
                if (typeof jspdf === 'undefined') {
                    throw new Error('jsPDF library not loaded');
                }
                
                // Get the report container
                const reportContainer = document.querySelector('.sales-report-section');
                if (!reportContainer) {
                    throw new Error('Report container not found');
                }
                
                // Show loading indicator
                const loadingIndicator = document.createElement('div');
                loadingIndicator.style.position = 'fixed';
                loadingIndicator.style.top = '50%';
                loadingIndicator.style.left = '50%';
                loadingIndicator.style.transform = 'translate(-50%, -50%)';
                loadingIndicator.style.padding = '20px';
                loadingIndicator.style.background = 'rgba(0, 0, 0, 0.7)';
                loadingIndicator.style.color = 'white';
                loadingIndicator.style.borderRadius = '5px';
                loadingIndicator.style.zIndex = '9999';
                loadingIndicator.textContent = 'Generating PDF...';
                document.body.appendChild(loadingIndicator);
                
                // Use html2canvas to capture the report
                html2canvas(reportContainer, {
                    scale: 1,
                    useCORS: true,
                    logging: true,
                    onclone: function(clonedDoc) {
                        // Make any adjustments to the cloned document if needed
                        const clonedReport = clonedDoc.querySelector('.sales-report-section');
                        if (clonedReport) {
                            clonedReport.style.width = '100%';
                            clonedReport.style.height = 'auto';
                            clonedReport.style.overflow = 'visible';
                        }
                    }
                }).then(function(canvas) {
                    try {
                        // Create PDF
                        const { jsPDF } = jspdf;
                        const pdf = new jsPDF('p', 'mm', 'a4');
                        
                        // Get canvas dimensions
                        const imgData = canvas.toDataURL('image/png');
                        const imgWidth = 210; // A4 width in mm
                        const pageHeight = 295; // A4 height in mm
                        const imgHeight = canvas.height * imgWidth / canvas.width;
                        let heightLeft = imgHeight;
                        let position = 0;
                        
                        // Add image to first page
                        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                        heightLeft -= pageHeight;
                        
                        // Add additional pages if needed
                        while (heightLeft > 0) {
                            position = heightLeft - imgHeight;
                            pdf.addPage();
                            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                            heightLeft -= pageHeight;
                        }
                        
                        // Save the PDF
                        pdf.save('sales_report.pdf');
                        console.log('PDF generated successfully');
                        
                        // Remove loading indicator
                        document.body.removeChild(loadingIndicator);
                    } catch (err) {
                        console.error('Error creating PDF:', err);
                        alert('Error creating PDF: ' + err.message);
                        document.body.removeChild(loadingIndicator);
                    }
                }).catch(function(err) {
                    console.error('Error capturing report:', err);
                    alert('Error capturing report: ' + err.message);
                    document.body.removeChild(loadingIndicator);
                });
            } catch (err) {
                console.error('Error in PDF generation setup:', err);
                alert('Error setting up PDF generation: ' + err.message);
            }
        });
    } else {
        console.error('Download PDF button not found');
    }
});
