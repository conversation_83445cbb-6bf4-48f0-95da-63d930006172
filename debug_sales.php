<?php
// Debug script to check sales data
$conn = require_once 'config/database.php';

echo "<h2>Sales Table Debug Information</h2>";

// Check if sales table exists and get structure
echo "<h3>1. Sales Table Structure:</h3>";
$structure_query = "DESCRIBE sales";
$structure_result = mysqli_query($conn, $structure_query);

if ($structure_result) {
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = mysqli_fetch_assoc($structure_result)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Error: " . mysqli_error($conn);
}

// Check total number of sales records
echo "<h3>2. Total Sales Records:</h3>";
$count_query = "SELECT COUNT(*) as total FROM sales";
$count_result = mysqli_query($conn, $count_query);
if ($count_result) {
    $count_row = mysqli_fetch_assoc($count_result);
    echo "Total sales records: " . $count_row['total'];
} else {
    echo "Error: " . mysqli_error($conn);
}

// Show sample sales data
echo "<h3>3. Sample Sales Data:</h3>";
$sample_query = "SELECT * FROM sales LIMIT 5";
$sample_result = mysqli_query($conn, $sample_query);

if ($sample_result && mysqli_num_rows($sample_result) > 0) {
    echo "<table border='1'>";
    $first_row = true;
    while ($row = mysqli_fetch_assoc($sample_result)) {
        if ($first_row) {
            echo "<tr>";
            foreach (array_keys($row) as $column) {
                echo "<th>$column</th>";
            }
            echo "</tr>";
            $first_row = false;
        }
        echo "<tr>";
        foreach ($row as $value) {
            echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "No sales records found or error: " . mysqli_error($conn);
}

// Check sales statistics
echo "<h3>4. Sales Statistics:</h3>";
$stats_query = "SELECT 
    COUNT(*) as total_count,
    SUM(total_amount) as total_revenue,
    AVG(total_amount) as average_sale,
    MIN(sale_date) as earliest_sale,
    MAX(sale_date) as latest_sale
FROM sales";
$stats_result = mysqli_query($conn, $stats_query);

if ($stats_result) {
    $stats = mysqli_fetch_assoc($stats_result);
    echo "<ul>";
    echo "<li>Total Sales: " . ($stats['total_count'] ?? 0) . "</li>";
    echo "<li>Total Revenue: ₹" . number_format($stats['total_revenue'] ?? 0, 2) . "</li>";
    echo "<li>Average Sale: ₹" . number_format($stats['average_sale'] ?? 0, 2) . "</li>";
    echo "<li>Earliest Sale: " . ($stats['earliest_sale'] ?? 'N/A') . "</li>";
    echo "<li>Latest Sale: " . ($stats['latest_sale'] ?? 'N/A') . "</li>";
    echo "</ul>";
} else {
    echo "Error: " . mysqli_error($conn);
}

// Check contacts table for conversion rate calculation
echo "<h3>5. Contacts Statistics for Conversion Rate:</h3>";
$contacts_query = "SELECT
    COUNT(*) as total_contacts,
    COUNT(DISTINCT s.contact_id) as customer_contacts,
    (COUNT(*) - COUNT(DISTINCT s.contact_id)) as lead_contacts
FROM contacts c
LEFT JOIN sales s ON c.id = s.contact_id";
$contacts_result = mysqli_query($conn, $contacts_query);

if ($contacts_result) {
    $contacts = mysqli_fetch_assoc($contacts_result);
    $conversion_rate = 0;
    if ($contacts['total_contacts'] > 0) {
        $conversion_rate = ($contacts['customer_contacts'] / $contacts['total_contacts']) * 100;
    }
    echo "<ul>";
    echo "<li>Total Contacts: " . ($contacts['total_contacts'] ?? 0) . "</li>";
    echo "<li>Customer Contacts: " . ($contacts['customer_contacts'] ?? 0) . "</li>";
    echo "<li>Lead Contacts: " . ($contacts['lead_contacts'] ?? 0) . "</li>";
    echo "<li>Conversion Rate: " . round($conversion_rate, 1) . "%</li>";
    echo "</ul>";
} else {
    echo "Error: " . mysqli_error($conn);
}

mysqli_close($conn);
?>
