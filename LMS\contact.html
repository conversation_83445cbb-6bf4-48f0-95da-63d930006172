<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>LMS - Contact</title>
    <link rel="icon" type="image/png" href="/images/logo.title.png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet" />
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Open+Sans:wght@400;500;600&display=swap"
        rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" />
    <link href="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css" rel="stylesheet">
    <style>
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        .animate-fadeInUp { animation: fadeInUp 0.6s ease-out forwards; }
        .animate-float { animation: float 6s ease-in-out infinite; }
        .glass-effect {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .card-hover { transition: all 0.3s ease; }
        .card-hover:hover { transform: translateY(-10px); box-shadow: 0 25px 50px -12px rgba(0,0,0,0.25); }

           /* Hamburger to X animation for mobile menu button */
           #mobileMenuBtn.open span:nth-child(1) {
          transform: rotate(45deg) translateY(8px);
        }
        #mobileMenuBtn.open span:nth-child(2) {
          opacity: 0;
        }
        #mobileMenuBtn.open span:nth-child(3) {
          transform: rotate(-45deg) translateY(-8px);
        }
    </style>
</head>

<body class="overflow-x-hidden bg-gray-50 text-gray-800">
 
  <header class="fixed w-full top-0 z-50">
    <div class="lg:max-w-full mx-auto px-4 sm:px-4 md:px-6 lg:px-8 py-2 sm:py-3 md:py-4">
      <div
        class=" bg-white backdrop-blur-lg rounded-lg sm:rounded-xl lg:rounded-2xl shadow-lg border border-white/20 mx-1 sm:mx-2 md:mx-3 lg:mx-4">
        <div class=" flex justify-between items-center h-12 sm:h-14 md:h-16 px-2 sm:px-4 md:px-6">

          <div>
            <h1
              class="text-lg sm:text-xl md:text-2xl bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent relative">
              <img src="images/logo.png" alt="LMS Logo" class="lg:w-32  w-20 object-contain">
            </h1>
          </div>

          <!-- Desktop Nav -->
          <nav class="hidden lg:flex justify-between items-center">
            <!-- logo -->

            
            <div class="flex  ">
              <a href="index.html"
                class="flex nav-link px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-all duration-300">
                Home
              </a>
              <a href="About.html"
                class="flex px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-all duration-300">
                About
              </a>
              <a href="products.html"
                class=" flex px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-all duration-300">
                Products
              </a>

              <!-- Features Dropdown -->
              <div class="relative">
                <button id="featuresDropdownBtn"
                  class="px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-all duration-300 flex items-center">
                  Features
                  <i class="ri-arrow-down-s-line ml-1 transition-transform duration-200"></i>
                </button>
                <div id="featuresDropdownMenu" class="absolute top-full left-0 mt-2 w-64 bg-white rounded-xl shadow-2xl border border-gray-100 py-2 z-50 hidden">
                  <a href="leadmanagement.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-brain-line text-blue-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Lead Management</div>
                      <div class="text-xs text-gray-500">Smart lead prioritization</div>
                    </div>
                  </a>
                  <a href="salemanagement.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-settings-3-line text-green-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Sales Management</div>
                      <div class="text-xs text-gray-500">Streamline your process</div>
                    </div>
                  </a>
                  <a href="analytics.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-pie-chart-line text-purple-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Advanced Analytics</div>
                      <div class="text-xs text-gray-500">Detailed insights & reports</div>
                    </div>
                  </a>
                  <a href="taskmanager.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-smartphone-line text-orange-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Task Manager</div>
                      <div class="text-xs text-gray-500">Capture from anywhere</div>
                    </div>
                  </a>
                  <a href="customersupport.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-team-line text-indigo-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Customer Support</div>
                      <div class="text-xs text-gray-500">Work together effectively</div>
                    </div>
                  </a>
                  <a href="integration.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-teal-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-shield-check-line text-teal-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Integration</div>
                      <div class="text-xs text-gray-500">Connect seamlessly with tools</div>
                    </div>
                  </a>
                </div>
              </div>

            </div>
            <!-- login -->
            <div class="flex ">
              <a href="contact.html"
                class=" flex items-center font-bold px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black  transition-all duration-300">
                Contact
              </a>

              <a href="/LEADManagement/auth/login.php"
                class=" flex items-center bg-orange-500 rounded-3xl font-bold px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-white  transition-all duration-300">
                Login
              </a>
            </div>

          </nav>

          <!-- Mobile Menu Button -->
          <div class="lg:hidden z-50">
            <button id="mobileMenuBtn" class="focus:outline-none relative w-6 sm:w-7 md:w-8 h-6 sm:h-7 md:h-8">
              <span class="absolute top-1 left-0 w-6 sm:w-7 md:w-8 h-0.5 bg-black transition-transform duration-300"></span>
              <span class="absolute top-3 left-0 w-6 sm:w-7 md:w-8 h-0.5 bg-black transition-opacity duration-300"></span>
              <span class="absolute top-5 left-0 w-6 sm:w-7 md:w-8 h-0.5 bg-black transition-transform duration-300"></span>
            </button>
          </div>
        </div>

        <!-- Mobile Nav Menu -->
        <div id="mobileMenu" class="lg:hidden px-3 sm:px-4 md:px-6 pb-4 sm:pb-5 md:pb-6 pt-2 hidden">
          <div class="flex flex-col space-y-2 sm:space-y-3 md:space-y-4">
            <a href="index.html"
              class="text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200">
              Home
            </a>
            <a href="About.html"
              class="text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200">
              About
            </a>
            <a href="products.html"
              class="text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200">
              Products
            </a>
            <a href="contact.html"
              class="text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200">
              Contact
            </a>

            <!-- Mobile Features Section -->
            <div>
              <button id="mobileFeaturesBtn"
                class="w-full text-left text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200 flex items-center justify-between">
                <span>Features</span>
                <i class="ri-arrow-down-s-line transition-transform duration-200"></i>
              </button>

              <div id="mobileFeaturesMenu" class="ml-4 mt-2 space-y-2 hidden">
                <a href="leadmanagement.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-brain-line mr-2"></i>Lead Management
                </a>
                <a href="salemanagement.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-settings-3-line mr-2"></i>Email
                  Marketing
                </a>
                <a href="analytics.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-pie-chart-line mr-2"></i>Advanced Analytics
                </a>
                <a href="taskmanager.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-smartphone-line mr-2"></i>Task Manager
                </a>
                <a href="customersupport.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-team-line mr-2"></i>Customer Support
                </a>
                <a href="integration.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-shield-check-line mr-2"></i>Integration
                </a>
              </div>
            </div>

            <a href="/LEADManagement/auth/login.php"
              class="bg-[#FF6500] text-white px-4 sm:px-5 md:px-6 py-1.5 sm:py-2 md:py-2.5 text-sm sm:text-base md:text-lg rounded-full font-bold text-center mt-2 hover:bg-[#e55a00] transition-colors duration-200">
              Login
            </a>
          </div>
        </div>
      </div>
    </div>
  </header>


  <!-- Hero Section -->
  <section class="px-4 sm:px-6 md:px-10 py-16 sm:py-20 mt-16 relative overflow-hidden bg-gradient-to-br from-orange-50 via-blue-50 to-white">
    <div class="text-center w-auto mx-auto space-y-10">
      <div class="animate-fadeInUp">
        <h1 class="text-5xl sm:text-6xl md:text-7xl font-extrabold font-serif bg-gradient-to-r from-orange-500 to-blue-600 bg-clip-text text-transparent mb-4">Contact Us</h1>
        <p class="text-lg sm:text-2xl md:text-3xl text-gray-700 font-medium mt-6 px-2 max-w-3xl mx-auto">We're here to help you with your ideas, projects, or partnerships. Reach out and let's make something great together.</p>
        <div class="flex flex-wrap justify-center gap-4 mt-8">
          <span class="inline-block bg-white/80 text-blue-700 font-semibold px-4 py-2 rounded-full shadow">24/7 Support</span>
          <span class="inline-block bg-white/80 text-orange-600 font-semibold px-4 py-2 rounded-full shadow">Quick Response</span>
          <span class="inline-block bg-white/80 text-purple-700 font-semibold px-4 py-2 rounded-full shadow">Expert Team</span>
          <span class="inline-block bg-white/80 text-green-700 font-semibold px-4 py-2 rounded-full shadow">Personalized Solutions</span>
        </div>
      </div>
      <div class="absolute inset-0 pointer-events-none">
        <div class="absolute -top-20 left-1/4 w-32 h-32 bg-blue-700 rounded-full opacity-10 animate-float"></div>
        <div class="absolute -top-40 right-1/4 w-24 h-24 bg-orange-500 rounded-full opacity-50 animate-float" style="animation-delay: -2s;"></div>
        <div class="absolute top-10 left-[16%] w-16 h-16 bg-blue-900 rounded-full opacity-25 animate-float" style="animation-delay: -4s;"></div>
      </div>
    </div>
  </section>

  <!-- Why Contact Us Section -->
  <section class="py-16 px-4 sm:px-6 lg:px-12">
    <div class="w-auto mx-auto" data-aos="fade-up">
      <h2 class="text-4xl sm:text-5xl font-extrabold text-center bg-gradient-to-r from-orange-500 to-blue-500 bg-clip-text text-transparent mb-16">Why Contact Us?</h2>
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8" data-aos="fade-up">
        <div class="bg-white rounded-2xl shadow-lg p-8 flex flex-col items-center text-center card-hover" data-aos="fade-up">
          <span class="text-4xl mb-4 text-blue-500"><i class="ri-customer-service-2-line"></i></span>
          <h3 class="text-xl font-bold mb-2">Dedicated Support</h3>
          <p class="text-gray-600">Our team is always ready to assist you with any queries or issues.</p>
        </div>
        <div class="bg-white rounded-2xl shadow-lg p-8 flex flex-col items-center text-center card-hover" data-aos="fade-up">
          <span class="text-4xl mb-4 text-orange-500"><i class="ri-time-line"></i></span>
          <h3 class="text-xl font-bold mb-2">Fast Response</h3>
          <p class="text-gray-600">We value your time and ensure quick turnaround for all requests.</p>
        </div>
        <div class="bg-white rounded-2xl shadow-lg p-8 flex flex-col items-center text-center card-hover" data-aos="fade-up">
          <span class="text-4xl mb-4 text-purple-500"><i class="ri-user-star-line"></i></span>
          <h3 class="text-xl font-bold mb-2">Expert Guidance</h3>
          <p class="text-gray-600">Get advice and solutions from our experienced professionals.</p>
        </div>
        <div class="bg-white rounded-2xl shadow-lg p-8 flex flex-col items-center text-center card-hover" data-aos="fade-up">
          <span class="text-4xl mb-4 text-green-500"><i class="ri-hand-heart-line"></i></span>
          <h3 class="text-xl font-bold mb-2">Personalized Care</h3>
          <p class="text-gray-600">We tailor our support to your unique needs and business goals.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact Form Section (Glassmorphism) -->
  <section class="py-16 px-2 sm:px-6 md:px-8 lg:px-12 bg-gradient-to-br from-white via-blue-50 to-orange-50">
    <div class="relative w-full max-w-5xl mx-auto rounded-3xl p-1 bg-gradient-to-r from-orange-400 via-blue-400 to-purple-500 shadow-2xl overflow-hidden">
      <div class="grid grid-cols-1 md:grid-cols-2 glass-effect rounded-2xl bg-white/80 overflow-hidden">
        <!-- Left Side: Contact Info -->
        <div class="relative bg-gradient-to-br from-[#1E3E62]/90 to-blue-900/80 glass-effect text-white p-8 md:p-12 flex flex-col justify-between items-center shadow-2xl" data-aos="fade-up">
          <!-- Decorative Icon/Illustration -->
          <div class="flex flex-col items-center w-full">
            <div class="mb-6">
              <div class="w-16 h-16 rounded-full bg-gradient-to-tr from-orange-400 to-blue-500 flex items-center justify-center shadow-lg animate-float">
                <i class="ri-customer-service-2-line text-4xl text-white"></i>
              </div>
            </div>
            <span class="inline-block mb-4 px-4 py-1 rounded-full bg-green-500/90 text-white text-xs font-bold tracking-wide shadow">We're Online</span>
            <h2 class="text-3xl md:text-4xl font-extrabold mb-2 text-center">Let's get in touch</h2>
            <p class="mb-6 text-indigo-100 text-center text-base md:text-lg">We're open to talk about your project, ideas, or help you with your queries.</p>
            <div class="space-y-5 w-full">
              <div class="flex items-center gap-3 group cursor-pointer transition">
                <div class="w-10 h-10 flex items-center justify-center rounded-xl bg-white/10 border border-white/20 shadow">
                  <i class="ri-mail-line text-2xl text-orange-300"></i>
                </div>
                <span class="text-lg font-medium select-all group-hover:underline transition"><EMAIL></span>
                <span class="ml-2 text-xs text-green-300 opacity-0 group-hover:opacity-100 transition">Copy</span>
              </div>
              <div class="flex items-center gap-3 group cursor-pointer transition">
                <div class="w-10 h-10 flex items-center justify-center rounded-xl bg-white/10 border border-white/20 shadow">
                  <i class="ri-phone-line text-2xl text-blue-200"></i>
                </div>
                <span class="text-lg font-medium select-all group-hover:underline transition">+91 98765 43210</span>
                <span class="ml-2 text-xs text-green-300 opacity-0 group-hover:opacity-100 transition">Copy</span>
              </div>
              <div class="flex items-center gap-3">
                <div class="w-10 h-10 flex items-center justify-center rounded-xl bg-white/10 border border-white/20 shadow">
                  <i class="ri-map-pin-line text-2xl text-pink-200"></i>
                </div>
                <span class="text-lg font-medium">Gurgaon, Haryana</span>
              </div>
            </div>
          </div>
          <div class="mt-10 w-full text-center">
            <p class="text-xs md:text-sm text-indigo-200">&copy; 2025 LMS. All rights reserved.</p>
          </div>
        </div>
        <!-- Right Side: Contact Form -->
        <div class="p-8 md:p-12 flex flex-col justify-center">
          <h3 class="text-xl md:text-2xl font-semibold mb-4 md:mb-6">Contact Form</h3>
          <form method="POST" action="../submit_lead.php" class="space-y-6 animate-fadeInUp" id="contactForm">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="relative">
                <span class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"><i class="ri-user-line"></i></span>
                <input type="text" name="first_name" id="first_name" required placeholder=" " class="peer w-full pl-10 p-3 border border-gray-200 rounded-xl bg-white/70 focus:outline-none focus:ring-2 focus:ring-blue-400 transition" />
                <label for="first_name" class="absolute left-10 top-1/2 -translate-y-1/2 text-gray-500 pointer-events-none transition-all duration-200
                  peer-focus:-top-3 peer-focus:text-xs peer-focus:text-blue-600
                  peer-[.not-empty]:-top-3 peer-[.not-empty]:text-xs peer-[.not-empty]:text-blue-600
                  peer-placeholder-shown:top-1/2 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-500
                  bg-white/70 px-1">First Name</label>
              </div>
              <div class="relative">
                <span class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"><i class="ri-user-line"></i></span>
                <input type="text" name="last_name" id="last_name" required placeholder=" " class="peer w-full pl-10 p-3 border border-gray-200 rounded-xl bg-white/70 focus:outline-none focus:ring-2 focus:ring-blue-400 transition" />
                <label for="last_name" class="absolute left-10 top-1/2 -translate-y-1/2 text-gray-500 pointer-events-none transition-all duration-200
                  peer-focus:-top-3 peer-focus:text-xs peer-focus:text-blue-600
                  peer-[.not-empty]:-top-3 peer-[.not-empty]:text-xs peer-[.not-empty]:text-blue-600
                  peer-placeholder-shown:top-1/2 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-500
                  bg-white/70 px-1">Last Name</label>
              </div>
            </div>
            <div class="relative">
              <span class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"><i class="ri-mail-line"></i></span>
              <input type="email" name="email" id="email" required placeholder=" " class="peer w-full pl-10 p-3 border border-gray-200 rounded-xl bg-white/70 focus:outline-none focus:ring-2 focus:ring-blue-400 transition" />
              <label for="email" class="absolute left-10 top-1/2 -translate-y-1/2 text-gray-500 pointer-events-none transition-all duration-200
                peer-focus:-top-3 peer-focus:text-xs peer-focus:text-blue-600
                peer-[.not-empty]:-top-3 peer-[.not-empty]:text-xs peer-[.not-empty]:text-blue-600
                peer-placeholder-shown:top-1/2 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-500
                bg-white/70 px-1">Email Address</label>
            </div>
            <div class="relative">
              <span class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"><i class="ri-phone-line"></i></span>
              <input type="tel" name="phone" id="phone" required placeholder=" " class="peer w-full pl-10 p-3 border border-gray-200 rounded-xl bg-white/70 focus:outline-none focus:ring-2 focus:ring-blue-400 transition" />
              <label for="phone" class="absolute left-10 top-1/2 -translate-y-1/2 text-gray-500 pointer-events-none transition-all duration-200
                peer-focus:-top-3 peer-focus:text-xs peer-focus:text-blue-600
                peer-[.not-empty]:-top-3 peer-[.not-empty]:text-xs peer-[.not-empty]:text-blue-600
                peer-placeholder-shown:top-1/2 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-500
                bg-white/70 px-1">Phone Number</label>
            </div>
            <div class="relative">
              <span class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"><i class="ri-question-answer-line"></i></span>
              <select name="enquiry_type" id="enquiry_type" required class="peer w-full pl-10 p-3 border border-gray-200 rounded-xl bg-white/70 focus:outline-none focus:ring-2 focus:ring-blue-400 transition appearance-none">
                <option value="" disabled selected hidden></option>
                <option value="Finance">Finance</option>
                <option value="Support">Support</option>
                <option value="Sales">Sales</option>
                <option value="Claim">Claim</option>
                <option value="HR">HR</option>
              </select>
              <label for="enquiry_type" class="absolute left-10 top-1/2 -translate-y-1/2 text-gray-500 pointer-events-none transition-all duration-200
                peer-focus:-top-3 peer-focus:text-xs peer-focus:text-blue-600
                peer-[.not-empty]:-top-3 peer-[.not-empty]:text-xs peer-[.not-empty]:text-blue-600
                peer-placeholder-shown:top-1/2 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-500
                bg-white/70 px-1">Enquiry Type</label>
              <span class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none"><i class="ri-arrow-down-s-line"></i></span>
            </div>
            <div class="relative">
              <span class="absolute left-3 top-4 text-gray-400"><i class="ri-message-3-line"></i></span>
              <textarea name="message" id="message" rows="4" required placeholder=" " class="peer w-full pl-10 p-3 border border-gray-200 rounded-xl bg-white/70 focus:outline-none focus:ring-2 focus:ring-blue-400 transition resize-none"></textarea>
              <label for="message" class="absolute left-10 top-4 text-gray-500 pointer-events-none transition-all duration-200
                peer-focus:-top-3 peer-focus:text-xs peer-focus:text-blue-600
                peer-[.not-empty]:-top-3 peer-[.not-empty]:text-xs peer-[.not-empty]:text-blue-600
                peer-placeholder-shown:top-4 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-500
                bg-white/70 px-1">Your Message</label>
            </div>
            <input type="checkbox" name="botcheck" class="hidden" style="display: none;" />
            <button type="submit" id="submitBtn" class="w-full md:w-auto bg-gradient-to-r from-orange-500 to-blue-500 text-white px-8 py-3 rounded-xl font-bold shadow-lg hover:from-blue-500 hover:to-orange-500 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400">
              <span id="submitText">Send Message</span>
              <span id="submitLoader" class="hidden">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Sending...
              </span>
            </button>
            <div class="hidden" id="form-success"><p class="text-green-600 text-center mt-4 font-semibold">✅ Thank you for your interest! We will contact you soon.</p></div>
            <div class="hidden" id="form-error"><p class="text-red-600 text-center mt-4 font-semibold">❌ <span id="error-message">Something went wrong. Please try again.</span></p></div>
          </form>
        </div>
      </div>
    </div>
  </section>

  <!-- Meet Our Support Team Section (optional for content richness) -->
  <section class="py-20 px-4 sm:px-6 lg:px-10 bg-white">
    <div class="w-auto mx-auto" data-aos="fade-up">
      <div class="text-center mb-16" data-aos="fade-up">
        <h2 class="text-4xl md:text-5xl bg-gradient-to-r from-blue-500 to-orange-500 bg-clip-text text-transparent font-bold mb-6">Meet Our Support Team</h2>
        <p class="text-xl text-gray-600 font-medium max-w-3xl mx-auto">A passionate, friendly team dedicated to helping you succeed and answering your questions.</p>
      </div>
      <div class="grid gap-10 sm:grid-cols-2 lg:grid-cols-3" data-aos="fade-up">
        <div class="bg-white rounded-2xl shadow-xl p-8 flex flex-col items-center group hover:-translate-y-2 hover:shadow-2xl transition-all duration-300" data-aos="fade-up">
          <div class="relative mb-4">
            <img src="images/customer.jpg" alt="Support 1" class="w-28 h-28 rounded-full object-cover border-4 border-blue-200 shadow-lg group-hover:scale-105 transition-transform" />
            <span class="absolute bottom-2 right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full"></span>
          </div>
          <h3 class="text-xl font-bold mb-1 group-hover:text-blue-500 transition-colors">Ava Patel</h3>
          <p class="text-blue-400 mb-2 font-semibold">Support Lead</p>
          <p class="text-gray-600 text-center text-sm mb-4">Expert in customer care and technical support. Loves solving problems and making customers happy.</p>
          <div class="flex gap-3">
            <a href="#" class="text-gray-400 hover:text-blue-500"><i class="ri-linkedin-box-fill text-xl"></i></a>
            <a href="#" class="text-gray-400 hover:text-blue-500"><i class="ri-mail-line text-xl"></i></a>
          </div>
        </div>
        <div class="bg-white rounded-2xl shadow-xl p-8 flex flex-col items-center group hover:-translate-y-2 hover:shadow-2xl transition-all duration-300" data-aos="fade-up">
          <div class="relative mb-4">
            <img src="images/lead.jpg" alt="Support 2" class="w-28 h-28 rounded-full object-cover border-4 border-orange-200 shadow-lg group-hover:scale-105 transition-transform" />
            <span class="absolute bottom-2 right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full"></span>
          </div>
          <h3 class="text-xl font-bold mb-1 group-hover:text-orange-500 transition-colors">Priya Singh</h3>
          <p class="text-orange-400 mb-2 font-semibold">Customer Success</p>
          <p class="text-gray-600 text-center text-sm mb-4">Ensures every customer gets the most out of our platform. Friendly and always ready to help.</p>
          <div class="flex gap-3">
            <a href="#" class="text-gray-400 hover:text-orange-500"><i class="ri-linkedin-box-fill text-xl"></i></a>
            <a href="#" class="text-gray-400 hover:text-orange-500"><i class="ri-mail-line text-xl"></i></a>
          </div>
        </div>
        <div class="bg-white rounded-2xl shadow-xl p-8 flex flex-col items-center group hover:-translate-y-2 hover:shadow-2xl transition-all duration-300" data-aos="fade-up">
          <div class="relative mb-4">
            <img src="images/officemeeting.jpg" alt="Support 3" class="w-28 h-28 rounded-full object-cover border-4 border-green-200 shadow-lg group-hover:scale-105 transition-transform" />
            <span class="absolute bottom-2 right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full"></span>
          </div>
          <h3 class="text-xl font-bold mb-1 group-hover:text-green-500 transition-colors">Carlos Rivera</h3>
          <p class="text-green-400 mb-2 font-semibold">Tech Support</p>
          <p class="text-gray-600 text-center text-sm mb-4">Solves technical issues and guides users through every step. Patient and knowledgeable.</p>
          <div class="flex gap-3">
            <a href="#" class="text-gray-400 hover:text-green-500"><i class="ri-linkedin-box-fill text-xl"></i></a>
            <a href="#" class="text-gray-400 hover:text-green-500"><i class="ri-mail-line text-xl"></i></a>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer (copied and adapted from about.html for consistency) -->
  <footer class="bg-black text-white px-6 sm:px-8 lg:px-12 w-full text-sm sm:text-base">
    <div class="w-full">
      <div class="py-16 grid grid-cols-1 md:grid-cols-4 gap-10 text-center md:text-left">
        <div class="flex flex-col items-center md:items-start space-y-6">
          <img src="images/logo.png" alt="Logo" class="w-40 sm:w-48 h-auto bg-white" />
          <p class="text-gray-400 text-sm sm:text-base max-w-sm text-center md:text-left">Effortless payroll, seamless success – simplify your pay process today.</p>
          <div class="flex gap-5 justify-center md:justify-start">
            <a href="#" class="text-gray-400 hover:text-white transition"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"/></svg></a>
            <a href="#" class="text-gray-400 hover:text-white transition"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"/></svg></a>
            <a href="#" class="text-gray-400 hover:text-white transition"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M7.75 2h8.5A5.75 5.75 0 0122 7.75v8.5A5.75 5.75 0 0116.25 22h-8.5A5.75 5.75 0 012 16.25v-8.5A5.75 5.75 0 017.75 2zm0 1.5A4.25 4.25 0 003.5 7.75v8.5A4.25 4.25 0 007.75 20.5h8.5a4.25 4.25 0 004.25-4.25v-8.5A4.25 4.25 0 0016.25 3.5h-8.5zm8.75 2a1 1 0 110 2 1 1 0 010-2zM12 7a5 5 0 110 10 5 5 0 010-10zm0 1.5a3.5 3.5 0 100 7 3.5 3.5 0 000-7z"/></svg></a>
            <a href="#" class="text-gray-400 hover:text-white transition"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M4.98 3.5C4.98 4.61 4.1 5.5 3 5.5S1.02 4.61 1.02 3.5 1.9 1.5 3 1.5 4.98 2.39 4.98 3.5zM.5 6h5V20h-5V6zm7.5 0h4.7v1.785h.066c.655-1.24 2.255-2.535 4.634-2.535 4.953 0 5.867 3.26 5.867 7.5V20h-5v-6.417c0-1.531-.027-3.5-2.134-3.5-2.138 0-2.466 1.668-2.466 3.39V20h-5V6z"/></svg></a>
          </div>
        </div>
        <div class="space-y-4">
          <h3 class="text-base font-extrabold">Quick Links</h3>
          <ul class="space-y-2 text-gray-400 text-sm sm:text-base">
            <li><a href="index.html" class="hover:text-white">Home</a></li>
            <li><a href="about.html" class="hover:text-white">About Us</a></li>
            <li><a href="products.html" class="hover:text-white">Products</a></li>
            <li><a href="contact.html" class="hover:text-white">Contact Us</a></li>
            <li><a href="login.html" class="hover:text-white">Login</a></li>
          </ul>
        </div>
        <div class="space-y-4">
          <h3 class="text-base font-extrabold">Products</h3>
          <ul class="space-y-2 text-gray-400 text-sm sm:text-base">
            <li><a href="leadmanagement.html" class="hover:text-white">Lead Management</a></li>
            <li><a href="salemanagement.html" class="hover:text-white">Sales Management</a></li>
            <li><a href="analytics.html" class="hover:text-white">Advance Analytics</a></li>
            <li><a href="customersupport.html" class="hover:text-white">Customer Management</a></li>
            <li><a href="integration.html" class="hover:text-white">Integration</a></li>
          </ul>
        </div>
        <div class="space-y-4">
          <h3 class="text-lg font-extrabold">Contact</h3>
          <ul class="space-y-3 text-gray-400 text-sm">
            <li class="flex items-center justify-center md:justify-start gap-2"><svg class="w-4 h-4" stroke="currentColor" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" /></svg><a href="mailto:<EMAIL>" class="hover:text-white"><EMAIL></a></li>
            <li class="flex items-center justify-center md:justify-start gap-2"><svg class="w-4 h-4" stroke="currentColor" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" /></svg>+91 98765 43210</li>
            <li class="flex items-start justify-center md:justify-start gap-2"><svg class="w-4 h-4" stroke="currentColor" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" /></svg><span>Gurgaon, Haryana</span></li>
          </ul>
        </div>
      </div>
      <div class="border-t border-gray-800 py-4 flex flex-col sm:flex-row justify-between items-center text-gray-400 text-sm sm:text-base gap-4 text-center">
        <p>© 2025 LMS. All rights reserved.</p>
        <div class="flex gap-4">
          <a href="#" class="hover:text-white">Terms & Conditions</a>
          <a href="#" class="hover:text-white">Privacy Policy</a>
        </div>
      </div>
    </div>
  </footer>

  <script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>
  <script>
    AOS.init();
  </script>
 <script>
  // Simple dropdown toggle for Features
  document.addEventListener('DOMContentLoaded', function () {
    const btn = document.getElementById('featuresDropdownBtn');
    const menu = document.getElementById('featuresDropdownMenu');
    btn.addEventListener('click', function (e) {
      e.stopPropagation();
      menu.classList.toggle('hidden');
    });
    // Close dropdown when clicking outside
    document.addEventListener('click', function (e) {
      if (!btn.contains(e.target) && !menu.contains(e.target)) {
        menu.classList.add('hidden');
      }
    });
  });

  // Mobile menu toggle
  document.addEventListener('DOMContentLoaded', function () {
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const mobileMenu = document.getElementById('mobileMenu');
    mobileMenuBtn.addEventListener('click', function (e) {
      e.stopPropagation();
      mobileMenu.classList.toggle('hidden');
      mobileMenuBtn.classList.toggle('open'); // Toggle open class for X animation
    });
    document.addEventListener('click', function (e) {
      if (!mobileMenu.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
        mobileMenu.classList.add('hidden');
        mobileMenuBtn.classList.remove('open'); // Remove open class if clicking outside
      }
    });

    // Mobile features dropdown
    const mobileFeaturesBtn = document.getElementById('mobileFeaturesBtn');
    const mobileFeaturesMenu = document.getElementById('mobileFeaturesMenu');
    mobileFeaturesBtn.addEventListener('click', function (e) {
      e.stopPropagation();
      mobileFeaturesMenu.classList.toggle('hidden');
    });
    document.addEventListener('click', function (e) {
      if (!mobileFeaturesMenu.contains(e.target) && !mobileFeaturesBtn.contains(e.target)) {
        mobileFeaturesMenu.classList.add('hidden');
      }
    });
  });
</script>

<script>
  document.querySelectorAll('.peer').forEach(function(input) {
    function check() {
      if (input.value) {
        input.classList.add('not-empty');
      } else {
        input.classList.remove('not-empty');
      }
    }
    input.addEventListener('input', check);
    // On page load
    check();
  });

  // Handle form submission
  document.getElementById('contactForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const form = this;
    const submitBtn = document.getElementById('submitBtn');
    const submitText = document.getElementById('submitText');
    const submitLoader = document.getElementById('submitLoader');
    const successDiv = document.getElementById('form-success');
    const errorDiv = document.getElementById('form-error');
    const errorMessage = document.getElementById('error-message');

    // Show loading state
    submitBtn.disabled = true;
    submitText.classList.add('hidden');
    submitLoader.classList.remove('hidden');
    successDiv.classList.add('hidden');
    errorDiv.classList.add('hidden');

    // Create FormData object
    const formData = new FormData(form);

    // Send AJAX request
    fetch(form.action, {
      method: 'POST',
      body: formData
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Show success message
        successDiv.classList.remove('hidden');
        form.reset();
        // Reset all peer classes
        document.querySelectorAll('.peer').forEach(input => {
          input.classList.remove('not-empty');
        });
      } else {
        // Show error message
        errorMessage.textContent = data.message || 'Something went wrong. Please try again.';
        errorDiv.classList.remove('hidden');
      }
    })
    .catch(error => {
      console.error('Error:', error);
      errorMessage.textContent = 'Network error. Please check your connection and try again.';
      errorDiv.classList.remove('hidden');
    })
    .finally(() => {
      // Reset button state
      submitBtn.disabled = false;
      submitText.classList.remove('hidden');
      submitLoader.classList.add('hidden');
    });
  });
</script>

</body>

</html>