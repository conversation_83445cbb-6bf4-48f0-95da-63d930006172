<?php
// This file contains the common navigation sidebar for all dashboard pages

// Determine if the current script is in a subfolder (like /auth/ or /includes/)
$script_path = $_SERVER['SCRIPT_NAME'];
$is_in_subfolder = strpos($script_path, '/auth/') !== false || strpos($script_path, '/includes/') !== false;

// Helper for path prefix
$prefix = $is_in_subfolder ? '../' : '';
?>
<!-- Mobile/Tablet Menu Toggle Button -->
<button id="menu-toggle" class="fixed top-4 right-4 z-50 bg-[#0b192c] text-white px-3 py-1 shadow-lg hover:bg-[#1e3e62] transition-colors" onclick="document.getElementById('sidebar').classList.remove('-translate-x-full'); this.classList.add('hidden');">
  <i class="fas fa-bars text-xl"></i>
</button>

<style>
@media (min-width: 870px) {
  #menu-toggle {
    display: none !important;
  }
}
#menu-toggle.hidden {
  display: none !important;
}
</style>

<!-- Sidebar -->
<?php if (has_role("sales_rep")) : ?>
<!-- Sales Representative Sidebar -->
<div id="sidebar" class="fixed left-0 top-0 w-56 min-[870px]:w-60 h-screen bg-[#0b192C] flex flex-col justify-between items-center py-2 min-[870px]:py-4 z-20 transform -translate-x-full min-[870px]:translate-x-0 transition-transform duration-300 ease-in-out">
    <!-- Close button for mobile/tablet -->
    <button class="close-menu-btn absolute top-2 right-2 text-white min-[870px]:hidden hover:text-orange-500 transition-colors" onclick="document.getElementById('sidebar').classList.add('-translate-x-full'); document.getElementById('menu-toggle').classList.remove('hidden');">
      <i class="fas fa-times text-xl"></i>
    </button>
    
    <div class="mb-6 md:mb-8 lg:mb-10 mt-8 md:mt-0">
      <img src="<?php echo $prefix; ?>img/logo.png" alt="Logo" class="w-32 md:w-36 lg:w-40" />
    </div>
    <nav class="flex flex-col gap-4 md:gap-5 lg:gap-6">
      <a href="<?php echo $prefix; ?>sales_rep_dashboard.php" class="flex items-center gap-2 <?php echo is_active_page('sales_rep_dashboard.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <img src="<?php echo $prefix; ?>img/dashboard.png" alt="Dashboard" class="w-5 h-5 md:w-6 md:h-6" />
        <span class="text-base md:text-lg font-medium">Dashboard</span>
      </a>
      <a href="<?php echo $prefix; ?>my_contacts.php" class="flex items-center gap-2 <?php echo is_active_page('my_contacts.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <img src="<?php echo $prefix; ?>img/leads.png" alt="My Contacts" class="w-5 h-5 md:w-6 md:h-6" />
        <span class="text-base md:text-lg font-medium">My Contacts</span>
      </a>
      <a href="<?php echo $prefix; ?>my_sales.php" class="flex items-center gap-2 <?php echo is_active_page('my_sales.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <img src="<?php echo $prefix; ?>img/sales.png" alt="My Sales" class="w-5 h-5 md:w-6 md:h-6" />
        <span class="text-base md:text-lg font-medium">My Sales</span>
      </a>
      <a href="<?php echo $prefix; ?>create_sale.php" class="flex items-center gap-2 <?php echo is_active_page('create_sale.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <img src="<?php echo $prefix; ?>img/sales.png" alt="Create Sale" class="w-5 h-5 md:w-6 md:h-6" />
        <span class="text-base md:text-lg font-medium">Create Sale</span>
      </a>
      <a href="<?php echo $prefix; ?>products.php" class="flex items-center gap-2 <?php echo is_active_page('products.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <img src="<?php echo $prefix; ?>img/products.png" alt="Products" class="w-5 h-5 md:w-6 md:h-6" />
        <span class="text-base md:text-lg font-medium">Products</span>
      </a>
      <?php if (has_role("admin")) : ?>
      <a href="<?php echo $prefix; ?>employees.php" class="flex items-center gap-2 <?php echo is_active_page('employees.php') || is_active_page('edit_employee.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <i class="fas fa-users w-5 h-5 md:w-6 md:h-6 flex items-center justify-center"></i>
        <span class="text-base md:text-lg font-medium">Employees</span>
      </a>
      <?php endif; ?>
    </nav>
    <div class="flex flex-col items-center mb-2">
      <div class="flex items-center gap-2 bg-[#0a1525] rounded-lg p-2">
        <div class="w-8 h-8 rounded-full bg-white flex items-center justify-center">
          <img src="<?php echo $prefix; ?>img/user.png" alt="User" class="w-5 h-5" />
        </div>
        <div class="flex flex-col">
          <p class="font-semibold text-white text-xs leading-tight truncate max-w-[100px]">
            <?php echo htmlspecialchars($_SESSION["first_name"] . " " . $_SESSION["last_name"]); ?>
          </p>
          <p class="text-[10px] text-gray-300 leading-tight"><?php echo htmlspecialchars(ucfirst($_SESSION["role"])); ?></p>
        </div>
      </div>
      <a href="<?php echo $prefix; ?>auth/logout.php" id="logout-btn" class="mt-2 bg-[#FF6500] text-white px-2 py-1 rounded-md flex items-center gap-1 hover:bg-orange-600 text-xs">
        <i class="fas fa-sign-out-alt text-[10px]"></i>
        <span class="font-medium text-[10px]">LOG OUT</span>
      </a>
    </div>
</div>
<?php else : ?>
<!-- Admin Sidebar -->
<div id="sidebar" class="fixed left-0 top-0 w-56 min-[870px]:w-60 h-screen bg-[#0b192C] flex flex-col justify-between items-center py-2 min-[870px]:py-4 z-20 transform -translate-x-full min-[870px]:translate-x-0 transition-transform duration-300 ease-in-out">
    <button class="close-menu-btn absolute top-2 right-2 text-white min-[870px]:hidden p-2 hover:text-orange-500 transition-colors" onclick="document.getElementById('sidebar').classList.add('-translate-x-full'); document.getElementById('menu-toggle').classList.remove('hidden');">
      <i class="fas fa-times text-xl"></i>
    </button>
    <div class="mb-6 md:mb-8 lg:mb-10 mt-8 md:mt-0">
      <img src="<?php echo $prefix; ?>img/logo.png" alt="Logo" class="w-32 md:w-36 lg:w-40" />
      <!-- Notifications Bell -->
      <div class="relative mt-4 flex justify-center">
        <button id="notificationBell" class="relative text-white hover:text-orange-500 transition-colors">
          <i class="fas fa-bell text-xl"></i>
          <span id="notificationBadge" class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center hidden">0</span>
        </button>
        <!-- Notification Dropdown -->
        <div id="notificationDropdown" class="absolute top-8 left-1/2 transform -translate-x-1/2 w-80 bg-white rounded-lg shadow-lg border hidden z-50">
          <div class="p-4 border-b">
            <div class="flex justify-between items-center">
              <h3 class="font-semibold text-gray-900">Notifications</h3>
              <button id="markAllRead" class="text-sm text-blue-600 hover:text-blue-800">Mark all read</button>
            </div>
          </div>
          <div id="notificationList" class="max-h-64 overflow-y-auto">
            <div class="p-4 text-center text-gray-500">
              <i class="fas fa-bell-slash text-2xl mb-2"></i>
              <p>No new notifications</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <nav class="flex flex-col gap-4 md:gap-5 lg:gap-6">
      <a href="<?php echo $prefix; ?>admin.php" class="flex items-center gap-2 <?php echo is_active_page('admin.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <img src="<?php echo $prefix; ?>img/dashboard.png" alt="Admin" class="w-5 h-5 md:w-6 md:h-6" />
        <span class="text-base md:text-lg font-medium">Admin</span>
      </a>
      <a href="<?php echo $prefix; ?>leads.php" class="flex items-center gap-2 <?php echo is_active_page('leads.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <img src="<?php echo $prefix; ?>img/leads.png" alt="Leads" class="w-5 h-5 md:w-6 md:h-6" />
        <span class="text-base md:text-lg font-medium">Leads</span>
      </a>
      <a href="<?php echo $prefix; ?>contacts.php" class="flex items-center gap-2 <?php echo is_active_page('contacts.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <img src="<?php echo $prefix; ?>img/leads.png" alt="Contacts" class="w-5 h-5 md:w-6 md:h-6" />
        <span class="text-base md:text-lg font-medium">Customers</span>
      </a>
      <a href="<?php echo $prefix; ?>sales.php" class="flex items-center gap-2 <?php echo is_active_page('sales.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <img src="<?php echo $prefix; ?>img/sales.png" alt="Sales" class="w-5 h-5 md:w-6 md:h-6" />
        <span class="text-base md:text-lg font-medium">Sales</span>
      </a>
      <a href="<?php echo $prefix; ?>products.php" class="flex items-center gap-2 <?php echo is_active_page('products.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <img src="<?php echo $prefix; ?>img/products.png" alt="Products" class="w-5 h-5 md:w-6 md:h-6" />
        <span class="text-base md:text-lg font-medium">Products</span>
      </a>
      <?php if (has_role("admin")) : ?>
      <a href="<?php echo $prefix; ?>employees.php" class="flex items-center gap-2 <?php echo is_active_page('employees.php') || is_active_page('edit_employee.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <i class="fas fa-users w-5 h-5 md:w-6 md:h-6 flex items-center justify-center"></i>
        <span class="text-base md:text-lg font-medium">Employees</span>
      </a>
      <?php endif; ?>
    </nav>
    <div class="flex flex-col items-center mb-2">
      <div class="flex items-center gap-2 bg-[#0a1525] rounded-lg p-2">
        <div class="w-8 h-8 rounded-full bg-white flex items-center justify-center">
          <img src="<?php echo $prefix; ?>img/user.png" alt="User" class="w-5 h-5" />
        </div>
        <div class="flex flex-col">
          <p class="font-semibold text-white text-xs leading-tight truncate max-w-[100px]">
            <?php echo htmlspecialchars($_SESSION["first_name"] . " " . $_SESSION["last_name"]); ?>
          </p>
          <p class="text-[10px] text-gray-300 leading-tight"><?php echo htmlspecialchars(ucfirst($_SESSION["role"])); ?></p>
        </div>
      </div>
      <a href="<?php echo $prefix; ?>auth/logout.php" id="logout-btn" class="mt-2 bg-[#FF6500] text-white px-2 py-1 rounded-md flex items-center gap-1 hover:bg-orange-600 text-xs">
        <i class="fas fa-sign-out-alt text-[10px]"></i>
        <span class="font-medium text-[10px]">LOG OUT</span>
      </a>
    </div>
</div>
<?php endif; ?>

<!-- Notification System JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const notificationBell = document.getElementById('notificationBell');
    const notificationBadge = document.getElementById('notificationBadge');
    const notificationDropdown = document.getElementById('notificationDropdown');
    const notificationList = document.getElementById('notificationList');
    const markAllReadBtn = document.getElementById('markAllRead');

    let isDropdownOpen = false;

    // Toggle notification dropdown
    notificationBell.addEventListener('click', function(e) {
        e.stopPropagation();
        isDropdownOpen = !isDropdownOpen;
        notificationDropdown.classList.toggle('hidden', !isDropdownOpen);

        if (isDropdownOpen) {
            loadNotifications();
        }
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function() {
        if (isDropdownOpen) {
            isDropdownOpen = false;
            notificationDropdown.classList.add('hidden');
        }
    });

    // Prevent dropdown from closing when clicking inside
    notificationDropdown.addEventListener('click', function(e) {
        e.stopPropagation();
    });

    // Mark all as read
    markAllReadBtn.addEventListener('click', function() {
        fetch('<?php echo $prefix; ?>get_notifications.php?action=mark_all_read', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                loadNotifications();
                updateBadge(0);
            }
        });
    });

    // Load notifications
    function loadNotifications() {
        fetch('<?php echo $prefix; ?>get_notifications.php?action=get_unread')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayNotifications(data.notifications);
                updateBadge(data.unread_count);
            }
        })
        .catch(error => console.error('Error loading notifications:', error));
    }

    // Display notifications in dropdown
    function displayNotifications(notifications) {
        if (notifications.length === 0) {
            notificationList.innerHTML = \`
                <div class="p-4 text-center text-gray-500">
                    <i class="fas fa-bell-slash text-2xl mb-2"></i>
                    <p>No new notifications</p>
                </div>
            \`;
            return;
        }

        let html = '';
        notifications.forEach(notification => {
            const iconClass = getNotificationIcon(notification.type);
            html += \`
                <div class="p-3 border-b hover:bg-gray-50 cursor-pointer notification-item" data-id="\${notification.id}">
                    <div class="flex items-start gap-3">
                        <div class="flex-shrink-0">
                            <i class="\${iconClass} text-blue-500"></i>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900">\${notification.title}</p>
                            <p class="text-sm text-gray-600 truncate">\${notification.message}</p>
                            <p class="text-xs text-gray-400 mt-1">\${notification.time_ago}</p>
                        </div>
                    </div>
                </div>
            \`;
        });

        notificationList.innerHTML = html;

        // Add click handlers to mark as read
        document.querySelectorAll('.notification-item').forEach(item => {
            item.addEventListener('click', function() {
                const notificationId = this.dataset.id;
                markAsRead(notificationId);
                this.style.opacity = '0.5';
            });
        });
    }

    // Update notification badge
    function updateBadge(count) {
        if (count > 0) {
            notificationBadge.textContent = count > 99 ? '99+' : count;
            notificationBadge.classList.remove('hidden');
        } else {
            notificationBadge.classList.add('hidden');
        }
    }

    // Mark notification as read
    function markAsRead(notificationId) {
        const formData = new FormData();
        formData.append('notification_id', notificationId);

        fetch('<?php echo $prefix; ?>get_notifications.php?action=mark_read', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                loadNotifications();
            }
        });
    }

    // Get icon for notification type
    function getNotificationIcon(type) {
        switch (type) {
            case 'lead': return 'fas fa-user-plus';
            case 'sale': return 'fas fa-shopping-cart';
            case 'contact': return 'fas fa-address-book';
            default: return 'fas fa-info-circle';
        }
    }

    // Check for new notifications every 30 seconds
    setInterval(function() {
        if (!isDropdownOpen) {
            fetch('<?php echo $prefix; ?>get_notifications.php?action=get_unread')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateBadge(data.unread_count);

                    // Show browser notification for new notifications
                    if (data.unread_count > 0 && 'Notification' in window) {
                        if (Notification.permission === 'granted') {
                            // Only show notification if there are new ones
                            const latestNotification = data.notifications[0];
                            if (latestNotification) {
                                new Notification(latestNotification.title, {
                                    body: latestNotification.message,
                                    icon: '<?php echo $prefix; ?>img/minilogo.jpg'
                                });
                            }
                        } else if (Notification.permission !== 'denied') {
                            Notification.requestPermission();
                        }
                    }
                }
            })
            .catch(error => console.error('Error checking notifications:', error));
        }
    }, 30000); // Check every 30 seconds

    // Initial load
    loadNotifications();

    // Request notification permission on page load
    if ('Notification' in window && Notification.permission === 'default') {
        Notification.requestPermission();
    }
});
</script>
