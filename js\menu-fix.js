// Simple script to ensure toggle button is hidden when sidebar is open
(function() {
    // Run immediately
    fixMenuToggle();
    
    // Also run after a short delay to ensure DOM is fully loaded
    setTimeout(fixMenuToggle, 100);
    
    function fixMenuToggle() {
        const toggleBtn = document.getElementById('menu-toggle');
        const sidebar = document.getElementById('sidebar');
        
        if (!toggleBtn || !sidebar) return;
        
        // On small screens
        if (window.innerWidth < 870) {
            // If sidebar is open, hide toggle button
            if (!sidebar.classList.contains('-translate-x-full')) {
                toggleBtn.classList.add('hidden');
            } else {
                toggleBtn.classList.remove('hidden');
            }
        } else {
            // On large screens, always hide toggle button
            toggleBtn.classList.add('hidden');
        }
    }
    
    // Also run when window is resized
    window.addEventListener('resize', fixMenuToggle);
})();
