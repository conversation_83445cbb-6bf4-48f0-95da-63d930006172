<?php
// Get database connection
$conn = require_once 'get_db_connection.php';

// Create leads table if it doesn't exist
$sql = "CREATE TABLE IF NOT EXISTS leads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    first_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    enquiry_type VARCHAR(50),
    message TEXT,
    status ENUM('new', 'contacted', 'qualified', 'converted', 'closed') NOT NULL DEFAULT 'new',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes for better performance
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_status (status)
)";

if ($conn->query($sql)) {
    echo "Leads table created successfully!";
} else {
    echo "Error creating leads table: " . $conn->error;
}

// Close connection
$conn->close();
?>