/**
 * Direct PDF Generation
 * 
 * This script provides a direct approach to PDF generation for the sales report
 * using a different method than the other scripts.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Direct PDF script loaded');
    
    // Add a direct PDF download button
    const downloadBtn = document.getElementById('download-report-btn');
    
    if (downloadBtn) {
        downloadBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Direct PDF download initiated');
            
            // Show loading message
            const loadingMsg = document.createElement('div');
            loadingMsg.textContent = 'Generating PDF...';
            loadingMsg.style.position = 'fixed';
            loadingMsg.style.top = '50%';
            loadingMsg.style.left = '50%';
            loadingMsg.style.transform = 'translate(-50%, -50%)';
            loadingMsg.style.padding = '15px 20px';
            loadingMsg.style.backgroundColor = 'rgba(0,0,0,0.7)';
            loadingMsg.style.color = 'white';
            loadingMsg.style.borderRadius = '5px';
            loadingMsg.style.zIndex = '9999';
            document.body.appendChild(loadingMsg);
            
            try {
                // Get the report section
                const reportSection = document.querySelector('.sales-report-section');
                    
                if (!reportSection) {
                    throw new Error('Report section not found');
                }
                
                // Create a clone of the report section to avoid modifying the original
                const reportClone = reportSection.cloneNode(true);
                
                // Remove the report parameters section from the clone
                // First, try to find the section with the specific class we added
                const paramSection = reportClone.querySelector('.report-parameters-section');
                if (paramSection) {
                    paramSection.remove();
                    console.log('Report parameters section removed using class');
                } else {
                    // Fallback: try to find the section with the "Report Parameters" heading
                    const headings = reportClone.querySelectorAll('h4');
                    let paramSectionRemoved = false;
                    
                    headings.forEach(heading => {
                        if (heading.textContent.includes('Report Parameters')) {
                            // Find the parent container of the parameters section
                            let paramContainer = heading.closest('.no-print, .bg-gray-50');
                            if (paramContainer) {
                                paramContainer.remove();
                                paramSectionRemoved = true;
                                console.log('Report parameters section removed using heading');
                            }
                        }
                    });
                    
                    // If we still couldn't find it, try to find it by the input fields
                    if (!paramSectionRemoved) {
                        const dateInputs = reportClone.querySelectorAll('#report-date-from, #report-date-to');
                        if (dateInputs.length > 0) {
                            // Find the common ancestor container
                            let container = dateInputs[0].closest('.bg-gray-50, .no-print, .mb-6');
                            if (container) {
                                container.remove();
                                console.log('Report parameters section removed using date inputs');
                            }
                        }
                    }
                }
                
                // Also remove the generate report button if it exists
                const generateBtn = reportClone.querySelector('#generate-report-btn');
                if (generateBtn) {
                    const btnContainer = generateBtn.closest('.flex, .items-end, .justify-center');
                    if (btnContainer) {
                        btnContainer.remove();
                        console.log('Generate report button removed');
                    } else {
                        generateBtn.remove();
                    }
                }
                
                // Remove any other elements with the no-print class
                const noPrintElements = reportClone.querySelectorAll('.no-print');
                noPrintElements.forEach(element => {
                    element.remove();
                    console.log('Removed no-print element');
                });
                
                // Remove the export report section (with the download button)
                const exportSection = reportClone.querySelector('#download-report-btn');
                if (exportSection) {
                    const exportContainer = exportSection.closest('.flex.flex-col, .md\\:flex-row, .justify-between, .items-center');
                    if (exportContainer) {
                        exportContainer.remove();
                        console.log('Export report section removed');
                    }
                }
                
                // Remove the original title section
                const titleSection = reportClone.querySelector('.report-title-section');
                if (titleSection) {
                    titleSection.remove();
                    console.log('Original report title section removed');
                }
                
                // Create a new, clean header for the PDF
                const newHeader = document.createElement('div');
                newHeader.style.padding = '20px';
                newHeader.style.backgroundColor = '#1E3E62';
                newHeader.style.color = 'white';
                newHeader.style.marginBottom = '20px';
                
                // Create title element
                const newTitle = document.createElement('h2');
                newTitle.textContent = 'Sales Report';
                newTitle.style.fontSize = '24px';
                newTitle.style.fontWeight = 'bold';
                newTitle.style.margin = '0 0 5px 0';
                
                // Create date range element
                const dateRange = document.createElement('p');
                const fromDate = document.getElementById('report-date-from').value;
                const toDate = document.getElementById('report-date-to').value;
                const fromDateFormatted = new Date(fromDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
                const toDateFormatted = new Date(toDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
                dateRange.textContent = `Period: ${fromDateFormatted} to ${toDateFormatted}`;
                dateRange.style.fontSize = '14px';
                dateRange.style.margin = '0';
                
                // Add elements to header
                newHeader.appendChild(newTitle);
                newHeader.appendChild(dateRange);
                
                // Add the new header to the beginning of the report
                if (reportClone.firstChild) {
                    reportClone.insertBefore(newHeader, reportClone.firstChild);
                } else {
                    reportClone.appendChild(newHeader);
                }
                
                console.log('New clean header added to report');
                
                // Apply print-specific styles to the clone
                reportClone.style.width = '100%';
                reportClone.style.maxWidth = '800px';
                reportClone.style.margin = '0 auto';
                reportClone.style.padding = '0';
                reportClone.style.backgroundColor = 'white';
                
                // Add a footer with page number and date
                const footer = document.createElement('div');
                footer.style.padding = '10px 20px';
                footer.style.borderTop = '1px solid #eee';
                footer.style.marginTop = '30px';
                footer.style.color = '#666';
                footer.style.fontSize = '10px';
                footer.style.display = 'flex';
                footer.style.justifyContent = 'space-between';
                
                const companyInfo = document.createElement('span');
                companyInfo.textContent = '© LEAD Management System';
                
                const generatedDate = document.createElement('span');
                generatedDate.textContent = 'Generated: ' + new Date().toLocaleString();
                
                footer.appendChild(companyInfo);
                footer.appendChild(generatedDate);
                
                // Add the footer to the end of the report
                reportClone.appendChild(footer);
                
                // Create a temporary container for the report
                const tempContainer = document.createElement('div');
                tempContainer.style.position = 'absolute';
                tempContainer.style.left = '-9999px';
                tempContainer.appendChild(reportClone);
                document.body.appendChild(tempContainer);
                
                // Use html2canvas to capture the report
                html2canvas(reportClone).then(function(canvas) {
                    // Remove the temporary container
                    document.body.removeChild(tempContainer);
                    
                    // Convert canvas to image
                    const imgData = canvas.toDataURL('image/png');
                    
                    // Initialize jsPDF
                    const { jsPDF } = jspdf;
                    const pdf = new jsPDF('p', 'mm', 'a4');
                    
                    // Calculate dimensions
                    const imgWidth = 210; // A4 width in mm
                    const imgHeight = canvas.height * imgWidth / canvas.width;
                    
                    // Add image to PDF
                    pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
                    
                    // Save the PDF
                    pdf.save('sales_report.pdf');
                    
                    // Remove loading message
                    document.body.removeChild(loadingMsg);
                    
                    console.log('PDF generated successfully');
                }).catch(function(error) {
                    console.error('Error generating canvas:', error);
                    document.body.removeChild(loadingMsg);
                    document.body.removeChild(tempContainer);
                    alert('Error generating PDF: ' + error.message);
                    
                    // Show fallback option
                    document.getElementById('download-report-fallback').classList.remove('hidden');
                });
            } catch (error) {
                console.error('Error in PDF generation setup:', error);
                document.body.removeChild(loadingMsg);
                alert('Error setting up PDF generation: ' + error.message);
                
                // Show fallback option
                document.getElementById('download-report-fallback').classList.remove('hidden');
            }
        });
    }
});
