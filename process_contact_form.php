<?php
// Include database connection
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/phpmailer_config.php';

// Set headers to handle AJAX requests
header('Content-Type: application/json');

// Check if the request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405); // Method Not Allowed
    echo json_encode(['success' => false, 'message' => 'Only POST requests are allowed']);
    exit;
}

// Check for bot submissions (honeypot field)
if (!empty($_POST['botcheck'])) {
    // This is likely a bot submission, silently exit with success message
    echo json_encode(['success' => true, 'message' => 'Form submitted successfully']);
    exit;
}

// Validate required fields
$required_fields = ['first_name', 'last_name', 'email', 'phone', 'message'];
$errors = [];

foreach ($required_fields as $field) {
    if (empty($_POST[$field])) {
        $errors[] = ucfirst(str_replace('_', ' ', $field)) . ' is required';
    }
}

// Validate email
if (!empty($_POST['email']) && !filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
    $errors[] = 'Please enter a valid email address';
}

// If there are validation errors, return them
if (!empty($errors)) {
    http_response_code(400); // Bad Request
    echo json_encode(['success' => false, 'message' => 'Validation failed', 'errors' => $errors]);
    exit;
}

// Sanitize input data
$first_name = sanitize_input($_POST['first_name']);
$last_name = sanitize_input($_POST['last_name']);
$email = sanitize_input($_POST['email']);
$phone = sanitize_input($_POST['phone']);
$enquiry_type = sanitize_input($_POST['enquiry_type'] ?? '');
$message = sanitize_input($_POST['message']);

try {
    // Insert data into leads table
    $sql = "INSERT INTO leads (first_name, last_name, email, phone, enquiry_type, message) 
            VALUES (?, ?, ?, ?, ?, ?)";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ssssss", $first_name, $last_name, $email, $phone, $enquiry_type, $message);
    
    if ($stmt->execute()) {
        $lead_id = $stmt->insert_id;
        
        // Send notification email to all employees
        $employees_sql = "SELECT email FROM employees";
        $employees_result = $conn->query($employees_sql);
        
        if ($employees_result && $employees_result->num_rows > 0) {
            // Create email content
            $subject = "New Lead Notification - {$first_name} {$last_name}";
            $body = "
                <html>
                <head>
                    <style>
                        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                        h2 { color: #1E3E62; }
                        .lead-details { background-color: #f9f9f9; padding: 15px; border-radius: 5px; }
                        .footer { margin-top: 20px; font-size: 12px; color: #777; }
                    </style>
                </head>
                <body>
                    <div class='container'>
                        <h2>New Lead Notification</h2>
                        <p>A new lead has been submitted through the contact form:</p>
                        
                        <div class='lead-details'>
                            <p><strong>Name:</strong> {$first_name} {$last_name}</p>
                            <p><strong>Email:</strong> {$email}</p>
                            <p><strong>Phone:</strong> {$phone}</p>
                            <p><strong>Enquiry Type:</strong> {$enquiry_type}</p>
                            <p><strong>Message:</strong> {$message}</p>
                        </div>
                        
                        <p>Please log in to the dashboard to manage this lead.</p>
                        <p><a href='http://localhost/LEADManagement/leads.php'>View Lead in Dashboard</a></p>
                        
                        <div class='footer'>
                            <p>This is an automated message from the Lead Management System.</p>
                        </div>
                    </div>
                </body>
                </html>
            ";
            
            // Send email to each employee
            while ($employee = $employees_result->fetch_assoc()) {
                send_email($employee['email'], $subject, $body);
            }
        }
        
        // Return success response
        echo json_encode(['success' => true, 'message' => 'Thank you for contacting us! We will get back to you soon.']);
    } else {
        throw new Exception("Error inserting lead: " . $stmt->error);
    }
} catch (Exception $e) {
    // Log the error
    error_log("Lead submission error: " . $e->getMessage());
    
    // Return error response
    http_response_code(500); // Internal Server Error
    echo json_encode(['success' => false, 'message' => 'An error occurred while processing your request. Please try again later.']);
}
?>