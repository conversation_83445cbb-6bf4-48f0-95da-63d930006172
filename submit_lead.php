<?php
/**
 * Lead submission handler for frontend contact form
 * This processes form submissions from the website and creates leads
 */

// Include database configuration
$conn = require_once 'config/database.php';
require_once 'includes/functions.php';

// Set JSON response header
header('Content-Type: application/json');

// Function to send JSON response
function sendResponse($success, $message, $data = null) {
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data
    ]);
    exit;
}

// Function to send email notification to employees
function sendLeadNotification($conn, $lead_data) {
    // Get all employees who should receive notifications (admin, manager, sales_rep)
    $employees_sql = "SELECT id, email, CONCAT(first_name, ' ', last_name) as name 
                      FROM employees 
                      WHERE role IN ('admin', 'manager', 'sales_rep') 
                      AND email IS NOT NULL";
    $employees_result = mysqli_query($conn, $employees_sql);
    
    $notification_count = 0;
    
    while ($employee = mysqli_fetch_assoc($employees_result)) {
        // Create database notification
        $notification_sql = "INSERT INTO notifications (user_id, title, message, type, related_id) VALUES (?, ?, ?, 'lead', ?)";
        $notification_stmt = mysqli_prepare($conn, $notification_sql);
        
        $title = "New Lead Received";
        $message = "New lead: " . $lead_data['first_name'] . " " . $lead_data['last_name'] . " (" . $lead_data['email'] . ")";
        
        mysqli_stmt_bind_param($notification_stmt, "issi", $employee['id'], $title, $message, $lead_data['lead_id']);
        
        if (mysqli_stmt_execute($notification_stmt)) {
            $notification_count++;
        }
        
        // TODO: Add SMTP email sending here
        // For now, we'll just create database notifications
    }
    
    return $notification_count;
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, 'Invalid request method');
}

// Check for bot submissions
if (!empty($_POST['botcheck'])) {
    sendResponse(false, 'Bot submission detected');
}

// Validate and sanitize input
$required_fields = ['first_name', 'last_name', 'email', 'phone', 'enquiry_type', 'message'];
$lead_data = [];

foreach ($required_fields as $field) {
    if (empty($_POST[$field])) {
        sendResponse(false, "Missing required field: $field");
    }
    $lead_data[$field] = sanitize_input($_POST[$field]);
}

// Additional validation
if (!filter_var($lead_data['email'], FILTER_VALIDATE_EMAIL)) {
    sendResponse(false, 'Invalid email address');
}

// Validate phone number (Indian format)
$phone = preg_replace('/[^0-9]/', '', $lead_data['phone']);
if (strlen($phone) !== 10 || !preg_match('/^[6-9][0-9]{9}$/', $phone)) {
    sendResponse(false, 'Invalid phone number. Please enter a valid 10-digit Indian mobile number');
}

// Check for duplicate leads (same email or phone)
$duplicate_check_sql = "SELECT id FROM leads WHERE email = ? OR phone = ?";
$duplicate_stmt = mysqli_prepare($conn, $duplicate_check_sql);
mysqli_stmt_bind_param($duplicate_stmt, "ss", $lead_data['email'], $phone);
mysqli_stmt_execute($duplicate_stmt);
$duplicate_result = mysqli_stmt_get_result($duplicate_stmt);

if (mysqli_num_rows($duplicate_result) > 0) {
    sendResponse(false, 'A lead with this email or phone number already exists');
}

// Auto-assign lead to available sales rep (round-robin)
$assign_sql = "SELECT id FROM employees WHERE role = 'sales_rep' ORDER BY id LIMIT 1";
$assign_result = mysqli_query($conn, $assign_sql);
$assigned_to = null;

if ($assign_row = mysqli_fetch_assoc($assign_result)) {
    $assigned_to = $assign_row['id'];
}

// Prepare lead data for insertion
$insert_sql = "INSERT INTO leads (
    first_name, 
    last_name, 
    email, 
    phone, 
    source, 
    customer_interest, 
    status, 
    assigned_to,
    notes,
    created_at
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

$insert_stmt = mysqli_prepare($conn, $insert_sql);

$source = 'website';
$customer_interest = $lead_data['enquiry_type'];
$status = 'new';
$notes = "Contact Form Submission:\n" . $lead_data['message'];

mysqli_stmt_bind_param($insert_stmt, "sssssssss", 
    $lead_data['first_name'],
    $lead_data['last_name'], 
    $lead_data['email'],
    $phone,
    $source,
    $customer_interest,
    $status,
    $assigned_to,
    $notes
);

// Execute the insertion
if (mysqli_stmt_execute($insert_stmt)) {
    $lead_id = mysqli_insert_id($conn);
    $lead_data['lead_id'] = $lead_id;
    
    // Send notifications to employees
    $notification_count = sendLeadNotification($conn, $lead_data);
    
    // Log the successful submission
    error_log("New lead created: ID $lead_id, Name: {$lead_data['first_name']} {$lead_data['last_name']}, Email: {$lead_data['email']}");
    
    sendResponse(true, 'Thank you for your interest! We will contact you soon.', [
        'lead_id' => $lead_id,
        'notifications_sent' => $notification_count
    ]);
} else {
    error_log("Failed to create lead: " . mysqli_error($conn));
    sendResponse(false, 'Sorry, there was an error processing your request. Please try again.');
}
?>
