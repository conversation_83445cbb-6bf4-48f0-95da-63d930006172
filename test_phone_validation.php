<?php
// Get database connection using reliable method
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';
require_once 'includes/validation.php';

// Check if the user is logged in
ensure_session_started();
require_login();

$test_results = [];

// Test various phone numbers if form is submitted
$test_results = [];
$referrer_test_results = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['test_phone']) && !empty($_POST['test_phone'])) {
        $test_phone = $_POST['test_phone'];
        $test_results = get_phone_validation_result($test_phone);
    }

    if (isset($_POST['test_referrer_phone']) && !empty($_POST['test_referrer_phone'])) {
        $test_referrer_phone = $_POST['test_referrer_phone'];
        $referrer_test_results = get_phone_validation_result($test_referrer_phone);

        // Additional check: referrer phone cannot be same as contact phone
        if (!empty($_POST['test_phone']) && $_POST['test_referrer_phone'] === $_POST['test_phone']) {
            $referrer_test_results['valid'] = false;
            $referrer_test_results['message'] = 'Referrer phone cannot be the same as contact phone';
        }
    }
}

// Predefined test cases
$test_cases = [
    // Valid numbers
    '9876543210' => 'Valid Indian mobile',
    '8700768938' => 'Valid Jio number',
    '9123456780' => 'Valid Airtel number',
    
    // Invalid patterns
    '0000000000' => 'All zeros',
    '1111111111' => 'All same digits',
    '1234567890' => 'Ascending sequence',
    '9876543210' => 'Descending sequence',
    '1010101010' => 'Alternating pattern',
    '1122334455' => 'Repeating pairs',
    '5555555555' => 'Invalid starting digit',
    '123456789' => 'Only 9 digits',
    '12345678901' => '11 digits',
    '9999999999' => 'All nines',
    '8888888888' => 'All eights',
    '7777777777' => 'All sevens'
];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phone Validation Test - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="js/validation.js"></script>
</head>
<body class="bg-gray-100">

<?php include 'includes/navigation.php'; ?>

<!-- Main Content -->
<div class="ml-0 min-[870px]:ml-60 min-h-screen flex flex-col">
    <header class="bg-[#1E3E62] p-4 flex justify-between z-10 flex-col">
        <h1 class="text-2xl font-bold text-white mx-10 my-2">Enhanced Phone Validation Test</h1>
    </header>
    
    <main class="flex-1 p-10">
        <!-- Test Form -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">Test Phone Number Validation</h2>

            <form method="POST" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="test_phone" class="block text-sm font-medium text-gray-700 mb-2">
                            Contact Phone Number:
                        </label>
                        <input type="text"
                               name="test_phone"
                               id="test_phone"
                               value="<?php echo isset($_POST['test_phone']) ? htmlspecialchars($_POST['test_phone']) : ''; ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="Enter 10-digit mobile number">
                        <div id="validation-result" class="mt-2"></div>
                    </div>

                    <div>
                        <label for="test_referrer_phone" class="block text-sm font-medium text-gray-700 mb-2">
                            Referrer Phone Number:
                        </label>
                        <input type="text"
                               name="test_referrer_phone"
                               id="test_referrer_phone"
                               value="<?php echo isset($_POST['test_referrer_phone']) ? htmlspecialchars($_POST['test_referrer_phone']) : ''; ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="Enter referrer's mobile number">
                        <div id="referrer-validation-result" class="mt-2"></div>
                    </div>
                </div>

                <button type="submit" class="px-4 py-2 bg-[#0b192c] text-white rounded-sm hover:bg-[#1e3e62]">
                    <i class="fas fa-check"></i> Test Validation
                </button>
            </form>
            
            <?php if (!empty($test_results) || !empty($referrer_test_results)): ?>
            <div class="mt-6 space-y-4">
                <?php if (!empty($test_results)): ?>
                <div class="p-4 rounded-md border <?php echo $test_results['valid'] ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'; ?>">
                    <h3 class="font-semibold <?php echo $test_results['valid'] ? 'text-green-800' : 'text-red-800'; ?>">
                        Contact Phone Validation Result:
                    </h3>
                    <p class="<?php echo $test_results['valid'] ? 'text-green-700' : 'text-red-700'; ?>">
                        <?php echo htmlspecialchars($test_results['message']); ?>
                    </p>
                    <p class="text-sm text-gray-600 mt-2">
                        <strong>Status:</strong> <?php echo $test_results['valid'] ? 'Valid' : 'Invalid'; ?>
                    </p>
                </div>
                <?php endif; ?>

                <?php if (!empty($referrer_test_results)): ?>
                <div class="p-4 rounded-md border <?php echo $referrer_test_results['valid'] ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'; ?>">
                    <h3 class="font-semibold <?php echo $referrer_test_results['valid'] ? 'text-green-800' : 'text-red-800'; ?>">
                        Referrer Phone Validation Result:
                    </h3>
                    <p class="<?php echo $referrer_test_results['valid'] ? 'text-green-700' : 'text-red-700'; ?>">
                        <?php echo htmlspecialchars($referrer_test_results['message']); ?>
                    </p>
                    <p class="text-sm text-gray-600 mt-2">
                        <strong>Status:</strong> <?php echo $referrer_test_results['valid'] ? 'Valid' : 'Invalid'; ?>
                    </p>
                </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>
        </div>
        
        <!-- Predefined Test Cases -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">Predefined Test Cases</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <?php foreach ($test_cases as $phone => $description): ?>
                    <?php $result = get_phone_validation_result($phone); ?>
                    <div class="p-4 border rounded-md <?php echo $result['valid'] ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'; ?>">
                        <div class="font-mono text-lg font-semibold"><?php echo htmlspecialchars($phone); ?></div>
                        <div class="text-sm text-gray-600 mb-2"><?php echo htmlspecialchars($description); ?></div>
                        <div class="text-sm <?php echo $result['valid'] ? 'text-green-700' : 'text-red-700'; ?>">
                            <i class="fas <?php echo $result['valid'] ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
                            <?php echo htmlspecialchars($result['message']); ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        
        <!-- Validation Rules -->
        <div class="bg-white rounded-lg shadow p-6 mt-8">
            <h2 class="text-xl font-semibold mb-4">Validation Rules</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-semibold text-green-700 mb-2">✓ Valid Requirements:</h3>
                    <ul class="text-sm space-y-1 text-gray-700">
                        <li>• Exactly 10 digits</li>
                        <li>• Must start with 6, 7, 8, or 9</li>
                        <li>• Valid Indian mobile prefix</li>
                        <li>• No obvious patterns or sequences</li>
                        <li>• Maximum 3 consecutive same digits</li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="font-semibold text-red-700 mb-2">✗ Invalid Patterns:</h3>
                    <ul class="text-sm space-y-1 text-gray-700">
                        <li>• All same digits (1111111111)</li>
                        <li>• Ascending sequences (1234567890)</li>
                        <li>• Descending sequences (9876543210)</li>
                        <li>• Alternating patterns (1010101010)</li>
                        <li>• Common dummy numbers</li>
                        <li>• More than 3 consecutive same digits</li>
                    </ul>
                </div>
            </div>
        </div>
    </main>
</div>

<script>
// Real-time validation as user types
document.getElementById('test_phone').addEventListener('input', function() {
    const phone = this.value;
    const resultDiv = document.getElementById('validation-result');

    if (phone.length > 0) {
        const validation = validateIndianMobile(phone);

        resultDiv.innerHTML = `
            <div class="p-3 rounded-md border ${validation.valid ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}">
                <div class="text-sm font-medium ${validation.valid ? 'text-green-800' : 'text-red-800'}">
                    Client-side Validation:
                </div>
                <div class="text-sm ${validation.valid ? 'text-green-700' : 'text-red-700'}">
                    <i class="fas ${validation.valid ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                    ${validation.message}
                </div>
            </div>
        `;
    } else {
        resultDiv.innerHTML = '';
    }

    // Re-validate referrer phone if it exists
    const referrerPhone = document.getElementById('test_referrer_phone');
    if (referrerPhone && referrerPhone.value.length > 0) {
        referrerPhone.dispatchEvent(new Event('input'));
    }
});

// Real-time validation for referrer phone
document.getElementById('test_referrer_phone').addEventListener('input', function() {
    const referrerPhone = this.value;
    const contactPhone = document.getElementById('test_phone').value;
    const resultDiv = document.getElementById('referrer-validation-result');

    if (referrerPhone.length > 0) {
        const validation = validateIndianMobile(referrerPhone);
        let isValid = validation.valid;
        let message = validation.message;

        // Check if referrer phone is same as contact phone
        if (referrerPhone === contactPhone && contactPhone.length > 0) {
            isValid = false;
            message = 'Referrer phone cannot be the same as contact phone';
        }

        resultDiv.innerHTML = `
            <div class="p-3 rounded-md border ${isValid ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}">
                <div class="text-sm font-medium ${isValid ? 'text-green-800' : 'text-red-800'}">
                    Client-side Validation:
                </div>
                <div class="text-sm ${isValid ? 'text-green-700' : 'text-red-700'}">
                    <i class="fas ${isValid ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                    ${message}
                </div>
            </div>
        `;
    } else {
        resultDiv.innerHTML = '';
    }
});
</script>

</body>
</html>
