/**
 * Document Validation Utilities
 * 
 * This file contains utility functions for validating identity documents like Aadhar Card and PAN Card
 */

/**
 * Validates an Aadhar Card number
 * 
 * @param {string} aadharNumber - The Aadhar number to validate
 * @param {HTMLElement} resultContainer - The element to display validation results
 * @param {Function} callback - Optional callback function to execute after validation
 */
function validateAadharCard(aadharNumber, resultContainer, callback) {
    // Clear previous validation results
    if (resultContainer) {
        resultContainer.innerHTML = '';
    }
    
    // Basic validation - must be 12 digits
    if (!/^\d{12}$/.test(aadharNumber)) {
        if (resultContainer) {
            resultContainer.innerHTML = '<div class="text-red-600 font-medium">Enter 12 digit Aadhaar number without spaces or special characters</div>';
        }
        if (callback) callback(false);
        return false;
    }
    
    // Check if A<PERSON><PERSON> is already registered (via AJAX)
    checkUniqueDocument('aadhar_card', aadhar<PERSON>umber, resultContainer, callback);
    return true;
}

/**
 * Validates a PAN Card number
 * 
 * @param {string} panNumber - The PAN number to validate
 * @param {HTMLElement} resultContainer - The element to display validation results
 * @param {Function} callback - Optional callback function to execute after validation
 */
function validatePANCard(panNumber, resultContainer, callback) {
    // Clear previous validation results
    if (resultContainer) {
        resultContainer.innerHTML = '';
    }
    
    // Convert to uppercase
    panNumber = panNumber.toUpperCase();
    
    // Basic validation - must match pattern [A-Z]{5}[0-9]{4}[A-Z]{1}
    if (!/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(panNumber)) {
        if (resultContainer) {
            resultContainer.innerHTML = '<div class="text-red-600 font-medium">Enter 10 character PAN number in format ********** (uppercase only)</div>';
        }
        if (callback) callback(false);
        return false;
    }
    
    // Check if PAN is already registered (via AJAX)
    checkUniqueDocument('pan_card', panNumber, resultContainer, callback);
    return true;
}

/**
 * Checks if a document is unique in the database
 * 
 * @param {string} fieldName - The field name to check (aadhar_card or pan_card)
 * @param {string} fieldValue - The value to check
 * @param {HTMLElement} resultContainer - The element to display validation results
 * @param {Function} callback - Optional callback function to execute after validation
 */
function checkUniqueDocument(fieldName, fieldValue, resultContainer, callback) {
    // Create form data
    const formData = new FormData();
    formData.append('field', fieldName);
    formData.append('value', fieldValue);
    formData.append('action', 'check_unique');
    
    // Send AJAX request
    fetch('ajax/check_unique_field.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.unique === false) {
            if (resultContainer) {
                if (fieldName === 'aadhar_card') {
                    resultContainer.innerHTML = '<div class="text-red-600 font-medium">This aadhar card is already registered with another customer.</div>';
                } else if (fieldName === 'pan_card') {
                    resultContainer.innerHTML = '<div class="text-red-600 font-medium">This pan card is already registered with another customer.</div>';
                }
            }
            if (callback) callback(false);
        } else {
            if (resultContainer) {
                resultContainer.innerHTML = '<div class="text-green-600 font-medium">Valid</div>';
            }
            if (callback) callback(true);
        }
    })
    .catch(error => {
        console.error('Error checking document uniqueness:', error);
        if (callback) callback(false);
    });
}

// Initialize document validation on page load
document.addEventListener('DOMContentLoaded', function() {
    // Find Aadhar Card input fields
    const aadharInputs = document.querySelectorAll('input[name="aadhar_card"]');
    aadharInputs.forEach(input => {
        // Create validation result container if it doesn't exist
        let resultContainer = document.getElementById(input.id + '-validation-result');
        if (!resultContainer) {
            resultContainer = document.createElement('div');
            resultContainer.id = input.id + '-validation-result';
            resultContainer.className = 'mt-1';
            input.parentNode.insertBefore(resultContainer, input.nextSibling);
        }
        
        // Add input event listener
        input.addEventListener('input', function() {
            // Force digits only
            this.value = this.value.replace(/[^0-9]/g, '');
            
            // Limit to 12 digits
            if (this.value.length > 12) {
                this.value = this.value.slice(0, 12);
            }
        });
        
        // Add blur event listener for validation
        input.addEventListener('blur', function() {
            if (this.value.trim() !== '') {
                validateAadharCard(this.value, resultContainer);
            } else {
                resultContainer.innerHTML = '';
            }
        });
    });
    
    // Find PAN Card input fields
    const panInputs = document.querySelectorAll('input[name="pan_card"]');
    panInputs.forEach(input => {
        // Create validation result container if it doesn't exist
        let resultContainer = document.getElementById(input.id + '-validation-result');
        if (!resultContainer) {
            resultContainer = document.createElement('div');
            resultContainer.id = input.id + '-validation-result';
            resultContainer.className = 'mt-1';
            input.parentNode.insertBefore(resultContainer, input.nextSibling);
        }
        
        // Add input event listener
        input.addEventListener('input', function() {
            // Force uppercase and alphanumeric only
            this.value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
            
            // Limit to 10 characters
            if (this.value.length > 10) {
                this.value = this.value.slice(0, 10);
            }
        });
        
        // Add blur event listener for validation
        input.addEventListener('blur', function() {
            if (this.value.trim() !== '') {
                validatePANCard(this.value, resultContainer);
            } else {
                resultContainer.innerHTML = '';
            }
        });
    });
});
