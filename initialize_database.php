<?php
/**
 * Database Initialization Script
 * 
 * This script creates all necessary tables for the Lead Management System
 * with the new consolidated sales table structure.
 */

// Include database configuration
$conn = require_once 'config/database.php';

echo "<h1>Database Initialization</h1>";
echo "<p>Setting up the Lead Management System database...</p>";

// Function to execute SQL with error handling
function executeSqlWithLog($conn, $sql, $description) {
    echo "<p><strong>$description...</strong></p>";
    if (mysqli_query($conn, $sql)) {
        echo "<p style='color: green;'>✓ $description completed successfully.</p>";
        return true;
    } else {
        echo "<p style='color: red;'>✗ Error in $description: " . mysqli_error($conn) . "</p>";
        return false;
    }
}

try {
    // Start transaction
    mysqli_begin_transaction($conn);
    
    echo "<h2>Creating Tables...</h2>";
    
    // Create employees table
    $sql = "CREATE TABLE IF NOT EXISTS employees (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(100) NOT NULL UNIQUE,
        role ENUM('admin', 'sales_rep', 'manager') NOT NULL,
        first_name VARCHAR(50) NOT NULL,
        last_name VARCHAR(50) NOT NULL,
        remember_token VARCHAR(64) DEFAULT NULL,
        token_expiry DATETIME DEFAULT NULL,
        reset_token VARCHAR(255) DEFAULT NULL,
        reset_token_expiry DATETIME DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    executeSqlWithLog($conn, $sql, "Creating employees table");
    
    // Create unified contacts table (combines leads and customers)
    $sql = "CREATE TABLE IF NOT EXISTS contacts (
        id INT AUTO_INCREMENT PRIMARY KEY,

        -- Basic contact information
        first_name VARCHAR(50) NOT NULL,
        last_name VARCHAR(50) NOT NULL,
        email VARCHAR(100) NOT NULL,
        phone VARCHAR(20),
        country_code VARCHAR(10) DEFAULT '+91',

        -- Business information
        company VARCHAR(100),

        -- Address and location
        address TEXT,

        -- Identity documents (for customers)
        aadhar_card VARCHAR(12),
        pan_card VARCHAR(10),

        -- Lead/Customer specific fields
        source VARCHAR(50),
        customer_interest TEXT,
        category VARCHAR(50),
        notes TEXT,

        -- Status tracking (lead progression to customer)
        status ENUM('lead_new', 'lead_contacted', 'lead_qualified', 'lead_lost', 'customer_active', 'customer_inactive') NOT NULL DEFAULT 'lead_new',

        -- Assignment and ownership
        assigned_to INT,
        created_by INT,

        -- Timestamps
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        -- Foreign key constraints
        FOREIGN KEY (assigned_to) REFERENCES employees(id) ON DELETE SET NULL,
        FOREIGN KEY (created_by) REFERENCES employees(id) ON DELETE SET NULL,

        -- Indexes for better performance
        INDEX idx_email (email),
        INDEX idx_phone (phone),
        INDEX idx_assigned_to (assigned_to),
        INDEX idx_created_by (created_by),
        INDEX idx_first_name (first_name),
        INDEX idx_last_name (last_name)
    )";
    executeSqlWithLog($conn, $sql, "Creating unified contacts table");
    
    // Create nominees table (linked to contacts)
    $sql = "CREATE TABLE IF NOT EXISTS nominees (
        id INT AUTO_INCREMENT PRIMARY KEY,
        contact_id INT NOT NULL,
        name VARCHAR(100) NOT NULL,
        relationship VARCHAR(50) NOT NULL,
        aadhar_id VARCHAR(12) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (contact_id) REFERENCES contacts(id) ON DELETE CASCADE
    )";
    executeSqlWithLog($conn, $sql, "Creating nominees table");
    
    // Create products table
    $sql = "CREATE TABLE IF NOT EXISTS products (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        price DECIMAL(10,2) NOT NULL,
        category VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    executeSqlWithLog($conn, $sql, "Creating products table");
    
    // Create consolidated sales table
    $sql = "CREATE TABLE IF NOT EXISTS sales (
        id INT AUTO_INCREMENT PRIMARY KEY,
        
        -- Basic sale information
        contact_id INT NOT NULL,
        sales_rep_id INT,
        sale_date DATE NOT NULL,
        payment_method VARCHAR(50),
        notes TEXT,
        
        -- Product and pricing (consolidated from sale_items)
        product_id INT NOT NULL,
        quantity DECIMAL(10,2) NOT NULL DEFAULT 1,
        unit_price DECIMAL(10,2) NOT NULL,
        subtotal DECIMAL(10,2) NOT NULL,
        tax DECIMAL(10,2) DEFAULT 0,
        total_amount DECIMAL(10,2) NOT NULL,
        
        -- Invoice information (consolidated from invoices)
        invoice_number VARCHAR(50),
        invoice_status ENUM('pending', 'paid', 'cancelled') NOT NULL DEFAULT 'pending',
        invoice_date DATE,
        

        
        -- Timestamps
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        -- Foreign key constraints
        FOREIGN KEY (contact_id) REFERENCES contacts(id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
        FOREIGN KEY (sales_rep_id) REFERENCES employees(id) ON DELETE SET NULL,

        -- Indexes for better performance
        INDEX idx_contact_id (contact_id),
        INDEX idx_product_id (product_id),
        INDEX idx_sales_rep_id (sales_rep_id),
        INDEX idx_sale_date (sale_date),
        INDEX idx_invoice_number (invoice_number)
    )";
    executeSqlWithLog($conn, $sql, "Creating consolidated sales table");
    
    // Create referrals table
    $sql = "CREATE TABLE IF NOT EXISTS referrals (
        id INT AUTO_INCREMENT PRIMARY KEY,
        contact_id INT NOT NULL,
        referrer_name VARCHAR(100) NOT NULL,
        referrer_phone VARCHAR(20),
        referrer_email VARCHAR(100),
        relationship VARCHAR(50),
        status ENUM('pending', 'contacted', 'converted', 'lost') NOT NULL DEFAULT 'pending',
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (contact_id) REFERENCES contacts(id) ON DELETE CASCADE
    )";
    executeSqlWithLog($conn, $sql, "Creating referrals table");
    
    echo "<h2>Creating Default Data...</h2>";
    
    // Create default admin user
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $sql = "INSERT IGNORE INTO employees (username, password, email, role, first_name, last_name)
            VALUES ('admin', '$admin_password', '<EMAIL>', 'admin', 'System', 'Administrator')";
    executeSqlWithLog($conn, $sql, "Creating default admin user");
    
    // Create sample products
    $sql = "INSERT IGNORE INTO products (id, name, price, category) VALUES 
            (1, 'Basic Package', 1000.00, 'Service'),
            (2, 'Premium Package', 2500.00, 'Service'),
            (3, 'Enterprise Package', 5000.00, 'Service')";
    executeSqlWithLog($conn, $sql, "Creating sample products");
    
    // Commit transaction
    mysqli_commit($conn);
    
    echo "<h2 style='color: green;'>Database Initialization Completed Successfully!</h2>";
    echo "<p>The following has been set up:</p>";
    echo "<ul>";
    echo "<li>✓ All database tables created with consolidated sales structure</li>";
    echo "<li>✓ Default admin user created (username: admin, password: admin123)</li>";
    echo "<li>✓ Sample products added</li>";
    echo "<li>✓ Foreign key relationships established</li>";
    echo "<li>✓ Indexes created for better performance</li>";
    echo "</ul>";
    
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li>1. <a href='auth/login.php'>Login to the system</a> using admin/admin123</li>";
    echo "<li>2. Change the default admin password</li>";
    echo "<li>3. Add your customers, leads, and products</li>";
    echo "<li>4. Start recording sales</li>";
    echo "</ul>";
    
    echo "<p><a href='auth/login.php' style='background: #1E3E62; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Login</a></p>";
    
} catch (Exception $e) {
    // Rollback transaction on error
    mysqli_rollback($conn);
    echo "<h2 style='color: red;'>Database Initialization Failed!</h2>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration and try again.</p>";
}

// Close connection
mysqli_close($conn);
?>
