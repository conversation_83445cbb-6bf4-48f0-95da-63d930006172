<?php
// Get database connection
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();
ensure_session_started();
require_login();

// Handle lead status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'update_status' && isset($_POST['lead_id']) && isset($_POST['status'])) {
        $lead_id = intval($_POST['lead_id']);
        $status = sanitize_input($_POST['status']);
        
        // Update lead status
        $update_sql = "UPDATE leads SET status = ? WHERE id = ?";
        $update_stmt = mysqli_prepare($conn, $update_sql);
        mysqli_stmt_bind_param($update_stmt, "si", $status, $lead_id);
        
        if (mysqli_stmt_execute($update_stmt)) {
            // If status is 'converted', convert lead to contact
            if ($status === 'converted') {
                // Get lead details
                $lead_sql = "SELECT * FROM leads WHERE id = ?";
                $lead_stmt = mysqli_prepare($conn, $lead_sql);
                mysqli_stmt_bind_param($lead_stmt, "i", $lead_id);
                mysqli_stmt_execute($lead_stmt);
                $lead_result = mysqli_stmt_get_result($lead_stmt);
                
                if ($lead = mysqli_fetch_assoc($lead_result)) {
                    // Insert into contacts table
                    $contact_sql = "INSERT INTO contacts (first_name, last_name, email, phone, customer_interest, created_by) 
                                    VALUES (?, ?, ?, ?, ?, ?)";
                    $contact_stmt = mysqli_prepare($conn, $contact_sql);
                    $current_user_id = $_SESSION['user_id'];
                    $customer_interest = $lead['enquiry_type'] . ': ' . $lead['message'];
                    
                    mysqli_stmt_bind_param($contact_stmt, "sssssi", 
                        $lead['first_name'], 
                        $lead['last_name'], 
                        $lead['email'], 
                        $lead['phone'], 
                        $customer_interest,
                        $current_user_id
                    );
                    
                    if (mysqli_stmt_execute($contact_stmt)) {
                        $contact_id = mysqli_insert_id($conn);
                        
                        // Delete the lead
                        $delete_sql = "DELETE FROM leads WHERE id = ?";
                        $delete_stmt = mysqli_prepare($conn, $delete_sql);
                        mysqli_stmt_bind_param($delete_stmt, "i", $lead_id);
                        mysqli_stmt_execute($delete_stmt);
                        
                        // Redirect to the new contact
                        header("Location: view_contact.php?id=" . $contact_id . "&success=1");
                        exit;
                    }
                }
            }
            
            // Redirect back to leads page with success message
            header("Location: leads.php?success=1");
            exit;
        } else {
            $error_message = "Error updating lead status: " . mysqli_error($conn);
        }
    }
}

// Get all leads with pagination
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

// Search functionality
$search = isset($_GET['search']) ? sanitize_input($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? sanitize_input($_GET['status']) : '';

// Build the query
$where_clauses = [];
$params = [];
$types = '';

if (!empty($search)) {
    $where_clauses[] = "(first_name LIKE ? OR last_name LIKE ? OR email LIKE ? OR phone LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $types .= 'ssss';
}

if (!empty($status_filter)) {
    $where_clauses[] = "status = ?";
    $params[] = $status_filter;
    $types .= 's';
}

$where_sql = !empty($where_clauses) ? "WHERE " . implode(" AND ", $where_clauses) : "";

// Count total leads for pagination
$count_sql = "SELECT COUNT(*) as total FROM leads $where_sql";
$count_stmt = mysqli_prepare($conn, $count_sql);

if (!empty($params)) {
    mysqli_stmt_bind_param($count_stmt, $types, ...$params);
}

mysqli_stmt_execute($count_stmt);
$count_result = mysqli_stmt_get_result($count_stmt);
$count_row = mysqli_fetch_assoc($count_result);
$total_leads = $count_row['total'];
$total_pages = ceil($total_leads / $limit);

// Get leads for current page
$leads_sql = "SELECT * FROM leads $where_sql ORDER BY created_at DESC LIMIT ? OFFSET ?";
$leads_stmt = mysqli_prepare($conn, $leads_sql);

if (!empty($params)) {
    $params[] = $limit;
    $params[] = $offset;
    $types .= 'ii';
    mysqli_stmt_bind_param($leads_stmt, $types, ...$params);
} else {
    mysqli_stmt_bind_param($leads_stmt, "ii", $limit, $offset);
}

mysqli_stmt_execute($leads_stmt);
$leads_result = mysqli_stmt_get_result($leads_stmt);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title>Leads Management - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .text-color { color: #FF6500; }
        .bg-color { background-color: #FF6500; }
    </style>
</head>
<body class="">
<?php include 'includes/navigation.php'; ?>
<div class="ml-0 min-[870px]:ml-60 min-h-screen flex flex-col">
    <header class="bg-[#1E3E62] p-4 flex justify-between z-10 flex-col">
        <h1 class="text-2xl font-bold text-white mx-10 my-2">Leads Management</h1>
    </header>
    <main class="flex-1 p-10">
        <?php if (isset($_GET['success'])): ?>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
            Lead updated successfully!
        </div>
        <?php endif; ?>
        
        <?php if (!empty($error_message)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <?php echo $error_message; ?>
        </div>
        <?php endif; ?>
        
        <!-- Search and Filter -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <form method="GET" class="flex flex-wrap gap-4">
                <div class="flex-1 min-w-[200px]">
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                    <input type="text" id="search" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="Name, Email, Phone" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                <div class="w-full sm:w-auto">
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select id="status" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">All Statuses</option>
                        <option value="new" <?php echo $status_filter === 'new' ? 'selected' : ''; ?>>New</option>
                        <option value="contacted" <?php echo $status_filter === 'contacted' ? 'selected' : ''; ?>>Contacted</option>
                        <option value="qualified" <?php echo $status_filter === 'qualified' ? 'selected' : ''; ?>>Qualified</option>
                        <option value="converted" <?php echo $status_filter === 'converted' ? 'selected' : ''; ?>>Converted</option>
                        <option value="closed" <?php echo $status_filter === 'closed' ? 'selected' : ''; ?>>Closed</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button type="submit" class="bg-[#1E3E62] text-white px-4 py-2 rounded-md hover:bg-blue-700 transition">Filter</button>
                    <a href="leads.php" class="ml-2 bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300 transition">Reset</a>
                </div>
            </form>
        </div>
        
        <!-- Leads Table -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact Info</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry Type</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php if (mysqli_num_rows($leads_result) > 0): ?>
                            <?php while ($lead = mysqli_fetch_assoc($leads_result)): ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($lead['first_name'] . ' ' . $lead['last_name']); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900"><?php echo htmlspecialchars($lead['email']); ?></div>
                                        <div class="text-sm text-gray-500"><?php echo htmlspecialchars($lead['phone']); ?></div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="text-sm text-gray-900"><?php echo htmlspecialchars($lead['enquiry_type']); ?></div>
                                        <div class="text-sm text-gray-500 truncate max-w-[200px]" title="<?php echo htmlspecialchars($lead['message']); ?>">
                                            <?php echo htmlspecialchars(substr($lead['message'], 0, 50) . (strlen($lead['message']) > 50 ? '...' : '')); ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                            <?php 
                                            switch ($lead['status']) {
                                                case 'new': echo 'bg-blue-100 text-blue-800'; break;
                                                case 'contacted': echo 'bg-yellow-100 text-yellow-800'; break;
                                                case 'qualified': echo 'bg-green-100 text-green-800'; break;
                                                case 'converted': echo 'bg-purple-100 text-purple-800'; break;
                                                case 'closed': echo 'bg-gray-100 text-gray-800'; break;
                                                default: echo 'bg-gray-100 text-gray-800';
                                            }
                                            ?>">
                                            <?php echo ucfirst(htmlspecialchars($lead['status'])); ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo date('M d, Y', strtotime($lead['created_at'])); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button type="button" onclick="openLeadModal(<?php echo $lead['id']; ?>)" class="text-indigo-600 hover:text-indigo-900 mr-3">View</button>
                                        <button type="button" onclick="openStatusModal(<?php echo $lead['id']; ?>, '<?php echo $lead['status']; ?>')" class="text-green-600 hover:text-green-900">Update Status</button>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500">No leads found</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-700">
                            Showing <span class="font-medium"><?php echo $offset + 1; ?></span> to 
                            <span class="font-medium"><?php echo min($offset + $limit, $total_leads); ?></span> of 
                            <span class="font-medium"><?php echo $total_leads; ?></span> leads
                        </div>
                        <div class="flex space-x-2">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>" class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                    Previous
                                </a>
                            <?php endif; ?>
                            
                            <?php if ($page < $total_pages): ?>
                                <a href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>" class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                    Next
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </main>
</div>

<!-- Lead View Modal -->
<div id="leadViewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div class="p-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-bold text-gray-900">Lead Details</h2>
                <button type="button" onclick="closeLeadModal()" class="text-gray-400 hover:text-gray-500">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="leadDetails" class="space-y-4">
                <!-- Lead details will be loaded here -->
                <div class="flex justify-center">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                </div>
            </div>
            <div class="mt-6 flex justify-end">
                <button type="button" onclick="closeLeadModal()" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300 transition">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Status Update Modal -->
<div id="statusModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
        <div class="p-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-bold text-gray-900">Update Lead Status</h2>
                <button type="button" onclick="closeStatusModal()" class="text-gray-400 hover:text-gray-500">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="updateStatusForm" method="POST" class="space-y-4">
                <input type="hidden" name="action" value="update_status">
                <input type="hidden" id="lead_id" name="lead_id" value="">
                
                <div>
                    <label for="status_update" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select id="status_update" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="new">New</option>
                        <option value="contacted">Contacted</option>
                        <option value="qualified">Qualified</option>
                        <option value="converted">Converted to Customer</option>
                        <option value="closed">Closed (Not Interested)</option>
                    </select>
                </div>
                
                <div id="convertWarning" class="bg-yellow-100 text-yellow-800 p-3 rounded-md hidden">
                    <p><strong>Note:</strong> Converting this lead will create a new contact and remove it from the leads list.</p>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeStatusModal()" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300 transition">Cancel</button>
                    <button type="submit" class="bg-[#1E3E62] text-white px-4 py-2 rounded-md hover:bg-blue-700 transition">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Lead View Modal Functions
    function openLeadModal(leadId) {
        document.getElementById('leadViewModal').classList.remove('hidden');
        document.getElementById('leadDetails').innerHTML = '<div class="flex justify-center"><div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div></div>';
        
        // Fetch lead details
        fetch(`get_lead_details.php?id=${leadId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('leadDetails').innerHTML = data.html;
                } else {
                    document.getElementById('leadDetails').innerHTML = `<div class="text-red-500">${data.message}</div>`;
                }
            })
            .catch(error => {
                document.getElementById('leadDetails').innerHTML = `<div class="text-red-500">Error loading lead details. Please try again.</div>`;
                console.error('Error:', error);
            });
    }
    
    function closeLeadModal() {
        document.getElementById('leadViewModal').classList.add('hidden');
    }
    
    // Status Update Modal Functions
    function openStatusModal(leadId, currentStatus) {
        document.getElementById('statusModal').classList.remove('hidden');
        document.getElementById('lead_id').value = leadId;
        document.getElementById('status_update').value = currentStatus;
        
        // Show warning if status is 'converted'
        checkConvertWarning();
    }
    
    function closeStatusModal() {
        document.getElementById('statusModal').classList.add('hidden');
    }
    
    function checkConvertWarning() {
        const statusSelect = document.getElementById('status_update');
        const convertWarning = document.getElementById('convertWarning');
        
        if (statusSelect.value === 'converted') {
            convertWarning.classList.remove('hidden');
        } else {
            convertWarning.classList.add('hidden');
        }
    }
    
    // Add event listener to status select
    document.getElementById('status_update').addEventListener('change', checkConvertWarning);
    
    // Close modals when clicking outside
    window.addEventListener('click', function(event) {
        const leadModal = document.getElementById('leadViewModal');
        const statusModal = document.getElementById('statusModal');
        
        if (event.target === leadModal) {
            closeLeadModal();
        }
        
        if (event.target === statusModal) {
            closeStatusModal();
        }
    });
</script>
</body>
</html>