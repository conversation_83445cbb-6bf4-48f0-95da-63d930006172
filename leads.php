<?php
// Include database configuration
$conn = require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if the user is logged in and has appropriate role
ensure_session_started();
require_any_role(["admin", "manager", "sales_rep"]);

// Get current user info
$current_user_id = $_SESSION['user_id'];
$current_user_role = $_SESSION['role'];

// Handle lead status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'update_status' && isset($_POST['lead_id'], $_POST['status'])) {
        $lead_id = intval($_POST['lead_id']);
        $new_status = sanitize_input($_POST['status']);
        
        $update_sql = "UPDATE leads SET status = ?, updated_at = NOW() WHERE id = ?";
        $update_stmt = mysqli_prepare($conn, $update_sql);
        mysqli_stmt_bind_param($update_stmt, "si", $new_status, $lead_id);
        
        if (mysqli_stmt_execute($update_stmt)) {
            // Create notification for status change
            $notification_sql = "INSERT INTO notifications (user_id, title, message, type, related_id) VALUES (?, ?, ?, 'lead', ?)";
            $notification_stmt = mysqli_prepare($conn, $notification_sql);
            $title = "Lead Status Updated";
            $message = "Lead #$lead_id status changed to " . ucfirst($new_status);
            mysqli_stmt_bind_param($notification_stmt, "issi", $current_user_id, $title, $message, $lead_id);
            mysqli_stmt_execute($notification_stmt);
            
            $success_message = "Lead status updated successfully!";
        } else {
            $error_message = "Error updating lead status.";
        }
    }
}

// Build query based on user role
if ($current_user_role === 'sales_rep') {
    // Sales reps see only their assigned leads
    $leads_sql = "SELECT l.*, CONCAT(e.first_name, ' ', e.last_name) as assigned_name 
                  FROM leads l 
                  LEFT JOIN employees e ON l.assigned_to = e.id 
                  WHERE l.assigned_to = ? 
                  ORDER BY l.created_at DESC";
    $leads_stmt = mysqli_prepare($conn, $leads_sql);
    mysqli_stmt_bind_param($leads_stmt, "i", $current_user_id);
} else {
    // Admins and managers see all leads
    $leads_sql = "SELECT l.*, CONCAT(e.first_name, ' ', e.last_name) as assigned_name 
                  FROM leads l 
                  LEFT JOIN employees e ON l.assigned_to = e.id 
                  ORDER BY l.created_at DESC";
    $leads_stmt = mysqli_prepare($conn, $leads_sql);
}

mysqli_stmt_execute($leads_stmt);
$leads_result = mysqli_stmt_get_result($leads_stmt);

// Get statistics
$stats_sql = "SELECT 
    COUNT(*) as total_leads,
    SUM(CASE WHEN status = 'new' THEN 1 ELSE 0 END) as new_leads,
    SUM(CASE WHEN status = 'contacted' THEN 1 ELSE 0 END) as contacted_leads,
    SUM(CASE WHEN status = 'qualified' THEN 1 ELSE 0 END) as qualified_leads,
    SUM(CASE WHEN status = 'converted' THEN 1 ELSE 0 END) as converted_leads,
    SUM(CASE WHEN status = 'lost' THEN 1 ELSE 0 END) as lost_leads
    FROM leads" . ($current_user_role === 'sales_rep' ? " WHERE assigned_to = $current_user_id" : "");

$stats_result = mysqli_query($conn, $stats_sql);
$stats = mysqli_fetch_assoc($stats_result);

// Get employees for assignment dropdown
$employees_sql = "SELECT id, CONCAT(first_name, ' ', last_name) as name FROM employees WHERE role IN ('sales_rep', 'manager') ORDER BY first_name";
$employees_result = mysqli_query($conn, $employees_sql);
$employees = [];
while ($emp = mysqli_fetch_assoc($employees_result)) {
    $employees[] = $emp;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title>Leads Management - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .text-color { color: #FF6500; }
        .status-new { background-color: #dbeafe; color: #1e40af; }
        .status-contacted { background-color: #fef3c7; color: #d97706; }
        .status-qualified { background-color: #d1fae5; color: #059669; }
        .status-converted { background-color: #dcfce7; color: #16a34a; }
        .status-lost { background-color: #fee2e2; color: #dc2626; }
    </style>
</head>
<body class="bg-gray-100">

<?php include 'includes/navigation.php'; ?>

<div class="flex">
    <div class="flex-1 ml-0 min-[870px]:ml-60 p-4">
        <main class="max-w-7xl mx-auto">
            <!-- Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">Leads Management</h1>
                <p class="text-gray-600">Manage and track your sales leads</p>
            </div>

            <!-- Success/Error Messages -->
            <?php if (isset($success_message)): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <?php if (isset($error_message)): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <!-- Statistics Cards -->
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
                <div class="bg-white p-4 rounded-lg shadow">
                    <div class="text-2xl font-bold text-blue-600"><?php echo $stats['total_leads']; ?></div>
                    <div class="text-sm text-gray-600">Total Leads</div>
                </div>
                <div class="bg-white p-4 rounded-lg shadow">
                    <div class="text-2xl font-bold text-blue-500"><?php echo $stats['new_leads']; ?></div>
                    <div class="text-sm text-gray-600">New</div>
                </div>
                <div class="bg-white p-4 rounded-lg shadow">
                    <div class="text-2xl font-bold text-yellow-600"><?php echo $stats['contacted_leads']; ?></div>
                    <div class="text-sm text-gray-600">Contacted</div>
                </div>
                <div class="bg-white p-4 rounded-lg shadow">
                    <div class="text-2xl font-bold text-green-600"><?php echo $stats['qualified_leads']; ?></div>
                    <div class="text-sm text-gray-600">Qualified</div>
                </div>
                <div class="bg-white p-4 rounded-lg shadow">
                    <div class="text-2xl font-bold text-green-700"><?php echo $stats['converted_leads']; ?></div>
                    <div class="text-sm text-gray-600">Converted</div>
                </div>
                <div class="bg-white p-4 rounded-lg shadow">
                    <div class="text-2xl font-bold text-red-600"><?php echo $stats['lost_leads']; ?></div>
                    <div class="text-sm text-gray-600">Lost</div>
                </div>
            </div>

            <!-- Leads Table -->
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">All Leads</h2>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lead</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assigned To</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Source</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php while ($lead = mysqli_fetch_assoc($leads_result)): ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">
                                        <?php echo htmlspecialchars($lead['first_name'] . ' ' . $lead['last_name']); ?>
                                    </div>
                                    <div class="text-sm text-gray-500">ID: #<?php echo $lead['id']; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?php echo htmlspecialchars($lead['email']); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo htmlspecialchars($lead['phone']); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full status-<?php echo $lead['status']; ?>">
                                        <?php echo ucfirst($lead['status']); ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo $lead['assigned_name'] ? htmlspecialchars($lead['assigned_name']) : 'Unassigned'; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo ucfirst($lead['source']); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo date('M d, Y', strtotime($lead['created_at'])); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <a href="view_lead.php?id=<?php echo $lead['id']; ?>" class="text-blue-600 hover:text-blue-900">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="edit_lead.php?id=<?php echo $lead['id']; ?>" class="text-green-600 hover:text-green-900">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <?php if ($lead['status'] !== 'converted'): ?>
                                        <button onclick="convertLead(<?php echo $lead['id']; ?>)" class="text-purple-600 hover:text-purple-900" title="Convert to Customer">
                                            <i class="fas fa-user-plus"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
function convertLead(leadId) {
    if (confirm('Are you sure you want to convert this lead to a customer?')) {
        // Redirect to conversion page
        window.location.href = 'convert_lead.php?id=' + leadId;
    }
}
</script>

</body>
</html>
