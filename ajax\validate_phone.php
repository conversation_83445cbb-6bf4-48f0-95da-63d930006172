<?php
/**
 * AJAX endpoint for phone number validation
 *
 * This script validates a phone number and returns the result as JSON.
 * It uses a simplified validation approach without external APIs for demonstration.
 */

// Include necessary files
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/validation.php';

// Set the content type to JSON
header('Content-Type: application/json');

// Initialize the response array
$response = [
    'success' => false,
    'html' => '',
    'data' => null
];

try {
    // Check if the request method is POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }
    
    // Check if the phone number is provided
    if (!isset($_POST['phone']) || empty($_POST['phone'])) {
        throw new Exception('Phone number is required');
    }
    
    // Get the phone number from the request
    $phone_number = $_POST['phone'];
    
    // Clean the phone number (remove non-digit characters except +)
    $clean_number = preg_replace('/[^0-9+]/', '', $phone_number);
    
    // Check for repeating digits (e.g., 0000000000, 9999999999)
    if (preg_match('/^(\d)\1{9}$/', $clean_number) || preg_match('/^\+\d{1,3}(\d)\1{9}$/', $clean_number)) {
        throw new Exception('Phone number cannot contain all repeating digits');
    }
    
    // Determine if it's a valid phone number format
    $is_valid = false;
    $country_code = null;
    $country_name = null;
    $carrier = null;
    $line_type = null;
    $region = null;
    $error = null;
    
    // Check if the number starts with a plus sign (international format)
    if (substr($clean_number, 0, 1) === '+') {
        // International format validation
        if (preg_match('/^\+[0-9]{1,3}[0-9]{7,15}$/', $clean_number)) {
            $is_valid = true;
            
            // Extract country code (simple approach)
            if (preg_match('/^\+([0-9]{1,3})/', $clean_number, $matches)) {
                $country_code = $matches[1];
                
                // Map common country codes to country names
                $country_map = [
                    '1' => 'United States/Canada',
                    '44' => 'United Kingdom',
                    '91' => 'India',
                    '61' => 'Australia',
                    '86' => 'China',
                    '49' => 'Germany',
                    '33' => 'France',
                    '81' => 'Japan',
                    '7' => 'Russia',
                    '55' => 'Brazil',
                    '34' => 'Spain',
                    '39' => 'Italy',
                    '52' => 'Mexico'
                ];
                
                $country_name = isset($country_map[$country_code]) ? $country_map[$country_code] : 'Unknown';
                
                // For India (91), provide more detailed information
                if ($country_code === '91') {
                    // Get the first 2 digits of the phone number (after +91)
                    $prefix = substr($clean_number, 3, 2);
                    
                    // Indian mobile number prefixes by carrier
                    $jio_prefixes = ['87', '88', '89', '70', '73', '74', '79'];
                    $airtel_prefixes = ['90', '91', '92', '93', '94', '95', '98', '99', '62', '63', '64', '65'];
                    $vodafone_idea_prefixes = ['71', '72', '73', '75', '76', '77', '78', '84', '85', '86', '96', '97'];
                    $bsnl_prefixes = ['94', '95', '96', '97'];
                    
                    // Check for specific number series
                    if (in_array($prefix, $jio_prefixes) || substr($clean_number, 3, 4) == '8700') {
                        $carrier = 'Reliance Jio';
                        $line_type = 'mobile';
                        $region = 'Delhi NCR';
                    } 
                    else if (in_array($prefix, $airtel_prefixes)) {
                        $carrier = 'Airtel';
                        $line_type = 'mobile';
                        $region = 'North India';
                    } 
                    else if (in_array($prefix, $vodafone_idea_prefixes)) {
                        $carrier = 'Vodafone Idea';
                        $line_type = 'mobile';
                        $region = 'North India';
                    } 
                    else if (in_array($prefix, $bsnl_prefixes)) {
                        $carrier = 'BSNL';
                        $line_type = 'mobile';
                        $region = 'North India';
                    } 
                    else if (in_array($prefix, ['11'])) {
                        $carrier = 'Landline';
                        $line_type = 'landline';
                        $region = 'Delhi';
                    } 
                    else if (in_array($prefix, ['22'])) {
                        $carrier = 'Landline';
                        $line_type = 'landline';
                        $region = 'Mumbai';
                    } 
                    else if (in_array($prefix, ['33'])) {
                        $carrier = 'Landline';
                        $line_type = 'landline';
                        $region = 'Kolkata';
                    } 
                    else if (in_array($prefix, ['44'])) {
                        $carrier = 'Landline';
                        $line_type = 'landline';
                        $region = 'Chennai';
                    } 
                    else {
                        // Default for unknown carriers
                        $carrier = 'Mobile Operator';
                        $line_type = 'mobile';
                        $region = 'India';
                    }
                }
            }
        } else {
            $error = 'Invalid international phone number format';
        }
    } else {
        // National format validation
        if (preg_match('/^(20|11|12|13|14|15|16|17|18|19)/', substr($clean_number, 0, 2))) { // UK
            $is_valid = true;
            $country_code = '44';
            $country_name = 'United Kingdom';
            $clean_number = '+44' . $clean_number;
            $carrier = 'UK Telecom Provider';
            $line_type = 'landline';
            $region = 'United Kingdom';
        } else if (preg_match('/^[0-9]{10}$/', $clean_number)) { // India
            $is_valid = true;
            $country_code = '91';
            $country_name = 'India';
            $clean_number = '+91' . $clean_number;
            $prefix = substr($clean_number, 3, 2);
            
            // Indian mobile number prefixes by carrier
            $jio_prefixes = ['87', '88', '89', '70', '73', '74', '79'];
            $airtel_prefixes = ['90', '91', '92', '93', '94', '95', '98', '99', '62', '63', '64', '65'];
            $vodafone_idea_prefixes = ['71', '72', '73', '75', '76', '77', '78', '84', '85', '86', '96', '97'];
            $bsnl_prefixes = ['94', '95', '96', '97'];

            if (in_array($prefix, $jio_prefixes) || substr($clean_number, 3, 4) == '8700') {
                $carrier = 'Reliance Jio';
                $line_type = 'mobile';
                $region = 'Delhi NCR';
            } else if (in_array($prefix, $airtel_prefixes)) {
                $carrier = 'Airtel';
                $line_type = 'mobile';
                $region = 'North India';
            } else if (in_array($prefix, $vodafone_idea_prefixes)) {
                $carrier = 'Vodafone Idea';
                $line_type = 'mobile';
                $region = 'North India';
            } else if (in_array($prefix, $bsnl_prefixes)) {
                $carrier = 'BSNL';
                $line_type = 'mobile';
                $region = 'North India';
            } else {
                $carrier = 'Mobile Operator';
                $line_type = 'mobile';
                $region = 'India';
            }
        } else {
            $error = 'Invalid phone number format. Must be 10 digits for Indian numbers or include country code (e.g., +91XXXXXXXXXX)';
        }
    }
    
    // Prepare the validation result
    $validation_result = [
        'is_valid' => $is_valid,
        'formatted_number' => $clean_number,
        'country_code' => $country_code,
        'country_name' => $country_name,
        'carrier' => $carrier,
        'line_type' => $line_type,
        'region' => $region,
        'error' => $error
    ];
    
    // Generate HTML for the validation result
    $html = '<div class="phone-validation-result mt-2 p-3 rounded-md border">';
    
    if ($error) {
        $html .= '<div class="text-red-600 font-medium">Error: ' . htmlspecialchars($error) . '</div>';
    } else {
        $html .= '<div class="grid grid-cols-2 gap-2 text-sm">';
        
        // Validity status with appropriate color
        $validity_class = $is_valid ? 'text-green-600' : 'text-red-600';
        $validity_text = $is_valid ? 'Valid' : 'Invalid';
        $html .= '<div class="font-medium">Status:</div>';
        $html .= '<div class="' . $validity_class . ' font-medium">' . $validity_text . '</div>';
        
        // Formatted number
        $html .= '<div class="font-medium">Formatted Number:</div>';
        $html .= '<div>' . htmlspecialchars($clean_number) . '</div>';
        
        // Country information
        if ($country_name) {
            $html .= '<div class="font-medium">Country:</div>';
            $html .= '<div>' . htmlspecialchars($country_name) . 
                     ' (' . htmlspecialchars($country_code) . ')</div>';
        }
        
        // Carrier information
        if ($carrier) {
            $html .= '<div class="font-medium">Carrier/Operator:</div>';
            $html .= '<div>' . htmlspecialchars($carrier) . '</div>';
        }
        
        // Line type information
        if ($line_type) {
            $html .= '<div class="font-medium">Line Type:</div>';
            $html .= '<div>' . htmlspecialchars(ucfirst($line_type)) . '</div>';
        }
        
        // Region information
        if ($region) {
            $html .= '<div class="font-medium">Region:</div>';
            $html .= '<div>' . htmlspecialchars($region) . '</div>';
        }
        
        $html .= '</div>';
    }
    
    $html .= '</div>';
    
    // Set the response
    $response['success'] = true;
    $response['html'] = $html;
    $response['data'] = $validation_result;
    
} catch (Exception $e) {
    // Set the error message
    $response['success'] = false;
    $response['html'] = '<div class="text-red-600">Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
}

// Return the response as JSON
echo json_encode($response);
exit;
