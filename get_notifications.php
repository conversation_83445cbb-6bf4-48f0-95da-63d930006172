<?php
/**
 * Get notifications for current user
 * This API endpoint returns unread notifications for real-time updates
 */

// Include database configuration
$conn = require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if the user is logged in
ensure_session_started();

if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$user_id = $_SESSION['user_id'];

// Set JSON response header
header('Content-Type: application/json');

// Get action parameter
$action = $_GET['action'] ?? 'get_unread';

switch ($action) {
    case 'get_unread':
        // Get unread notifications
        $sql = "SELECT id, title, message, type, related_id, created_at 
                FROM notifications 
                WHERE user_id = ? AND is_read = FALSE 
                ORDER BY created_at DESC 
                LIMIT 10";
        
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "i", $user_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        
        $notifications = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $row['created_at_formatted'] = date('M d, H:i', strtotime($row['created_at']));
            $row['time_ago'] = timeAgo($row['created_at']);
            $notifications[] = $row;
        }
        
        // Get total unread count
        $count_sql = "SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = FALSE";
        $count_stmt = mysqli_prepare($conn, $count_sql);
        mysqli_stmt_bind_param($count_stmt, "i", $user_id);
        mysqli_stmt_execute($count_stmt);
        $count_result = mysqli_stmt_get_result($count_stmt);
        $unread_count = mysqli_fetch_assoc($count_result)['count'];
        
        echo json_encode([
            'success' => true,
            'notifications' => $notifications,
            'unread_count' => $unread_count
        ]);
        break;
        
    case 'mark_read':
        // Mark notification as read
        if (!isset($_POST['notification_id'])) {
            echo json_encode(['success' => false, 'message' => 'Notification ID required']);
            exit;
        }
        
        $notification_id = intval($_POST['notification_id']);
        
        $update_sql = "UPDATE notifications SET is_read = TRUE WHERE id = ? AND user_id = ?";
        $update_stmt = mysqli_prepare($conn, $update_sql);
        mysqli_stmt_bind_param($update_stmt, "ii", $notification_id, $user_id);
        
        if (mysqli_stmt_execute($update_stmt)) {
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to mark as read']);
        }
        break;
        
    case 'mark_all_read':
        // Mark all notifications as read
        $update_all_sql = "UPDATE notifications SET is_read = TRUE WHERE user_id = ? AND is_read = FALSE";
        $update_all_stmt = mysqli_prepare($conn, $update_all_sql);
        mysqli_stmt_bind_param($update_all_stmt, "i", $user_id);
        
        if (mysqli_stmt_execute($update_all_stmt)) {
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to mark all as read']);
        }
        break;
        
    case 'get_all':
        // Get all notifications (read and unread)
        $limit = intval($_GET['limit'] ?? 20);
        $offset = intval($_GET['offset'] ?? 0);
        
        $sql = "SELECT id, title, message, type, related_id, is_read, created_at 
                FROM notifications 
                WHERE user_id = ? 
                ORDER BY created_at DESC 
                LIMIT ? OFFSET ?";
        
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "iii", $user_id, $limit, $offset);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        
        $notifications = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $row['created_at_formatted'] = date('M d, H:i', strtotime($row['created_at']));
            $row['time_ago'] = timeAgo($row['created_at']);
            $notifications[] = $row;
        }
        
        echo json_encode([
            'success' => true,
            'notifications' => $notifications
        ]);
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}

/**
 * Convert timestamp to "time ago" format
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) {
        return 'just now';
    } elseif ($time < 3600) {
        $minutes = floor($time / 60);
        return $minutes . ' minute' . ($minutes > 1 ? 's' : '') . ' ago';
    } elseif ($time < 86400) {
        $hours = floor($time / 3600);
        return $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ago';
    } elseif ($time < 2592000) {
        $days = floor($time / 86400);
        return $days . ' day' . ($days > 1 ? 's' : '') . ' ago';
    } else {
        return date('M d, Y', strtotime($datetime));
    }
}
?>
