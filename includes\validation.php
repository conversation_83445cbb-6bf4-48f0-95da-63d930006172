<?php
function validate_name($name) {
    // Only allow alphabetic characters and spaces
    return preg_match('/^[A-Za-z\s]+$/', $name);
}

function validate_phone($phone) {
    // Remove any non-digit characters
    $clean_phone = preg_replace('/[^0-9]/', '', $phone);

    // Check if it's exactly 10 digits
    if (strlen($clean_phone) !== 10) {
        return false;
    }

    // Use the comprehensive Indian mobile validation
    return validate_indian_mobile($clean_phone);
}

/**
 * Comprehensive validation for Indian mobile numbers
 * Ensures valid 10-digit Indian mobile number without dummy or pattern sequences
 *
 * @param string $phone 10-digit phone number (digits only)
 * @return bool True if valid, false otherwise
 */
function validate_indian_mobile($phone) {
    // Remove any non-digit characters
    $clean_phone = preg_replace('/[^0-9]/', '', $phone);

    // Must be exactly 10 digits
    if (strlen($clean_phone) !== 10) {
        return false;
    }

    // Indian mobile numbers must start with 6, 7, 8, or 9
    if (!preg_match('/^[6-9]/', $clean_phone)) {
        return false;
    }

    // Check for invalid patterns
    if (is_invalid_phone_pattern($clean_phone)) {
        return false;
    }

    // Check for valid Indian mobile number prefixes
    if (!has_valid_indian_prefix($clean_phone)) {
        return false;
    }

    return true;
}

/**
 * Check for invalid phone number patterns
 *
 * @param string $phone 10-digit phone number
 * @return bool True if invalid pattern detected, false if valid
 */
function is_invalid_phone_pattern($phone) {
    // Check for all same digits (0000000000, 1111111111, etc.)
    if (preg_match('/^(\d)\1{9}$/', $phone)) {
        return true;
    }

    // Check for ascending sequences (1234567890, 0123456789, etc.)
    if (is_ascending_sequence($phone)) {
        return true;
    }

    // Check for descending sequences (9876543210, 9876543210, etc.)
    if (is_descending_sequence($phone)) {
        return true;
    }

    // Check for alternating patterns (1010101010, 1212121212, etc.)
    if (is_alternating_pattern($phone)) {
        return true;
    }

    // Check for common dummy numbers
    $dummy_numbers = [
        '0000000000', '1111111111', '2222222222', '3333333333', '4444444444',
        '5555555555', '6666666666', '7777777777', '8888888888', '9999999999',
        '1234567890', '0123456789', '9876543210', '0987654321',
        '1234567891', '1234567892', '1234567893', '1234567894', '1234567895',
        '9876543211', '9876543212', '9876543213', '9876543214', '9876543215',
        '1010101010', '2020202020', '3030303030', '4040404040', '5050505050',
        '6060606060', '7070707070', '8080808080', '9090909090',
        '1212121212', '2323232323', '3434343434', '4545454545', '5656565656',
        '6767676767', '7878787878', '8989898989', '9090909090',
        '1122334455', '2233445566', '3344556677', '4455667788', '5566778899',
        '6677889900', '7788990011', '8899001122', '9900112233',
        '9999999999', '8888888888', '7777777777', '6666666666',
        '1000000000', '2000000000', '3000000000', '4000000000', '5000000000',
        '6000000000', '7000000000', '8000000000', '9000000000',
        '9123456789', '8123456789', '7123456789', '6123456789'
    ];

    if (in_array($phone, $dummy_numbers)) {
        return true;
    }

    // Check for too many repeating consecutive digits (more than 3 in a row)
    if (preg_match('/(\d)\1{3,}/', $phone)) {
        return true;
    }

    // Check for simple patterns like 1122334455, 1133557799, etc.
    if (preg_match('/^(\d)\1(\d)\2(\d)\3(\d)\4(\d)\5$/', $phone)) {
        return true;
    }

    return false;
}

/**
 * Check if phone number is an ascending sequence
 *
 * @param string $phone 10-digit phone number
 * @return bool True if ascending sequence, false otherwise
 */
function is_ascending_sequence($phone) {
    $digits = str_split($phone);
    $ascending_count = 0;

    for ($i = 1; $i < count($digits); $i++) {
        $current = (int)$digits[$i];
        $previous = (int)$digits[$i-1];

        // Handle wrap-around (9 to 0)
        if (($current == $previous + 1) || ($previous == 9 && $current == 0)) {
            $ascending_count++;
        } else {
            $ascending_count = 0;
        }

        // If we have 6 or more consecutive ascending digits, it's invalid
        if ($ascending_count >= 6) {
            return true;
        }
    }

    return false;
}

/**
 * Check if phone number is a descending sequence
 *
 * @param string $phone 10-digit phone number
 * @return bool True if descending sequence, false otherwise
 */
function is_descending_sequence($phone) {
    $digits = str_split($phone);
    $descending_count = 0;

    for ($i = 1; $i < count($digits); $i++) {
        $current = (int)$digits[$i];
        $previous = (int)$digits[$i-1];

        // Handle wrap-around (0 to 9)
        if (($current == $previous - 1) || ($previous == 0 && $current == 9)) {
            $descending_count++;
        } else {
            $descending_count = 0;
        }

        // If we have 6 or more consecutive descending digits, it's invalid
        if ($descending_count >= 6) {
            return true;
        }
    }

    return false;
}

/**
 * Check if phone number has alternating pattern
 *
 * @param string $phone 10-digit phone number
 * @return bool True if alternating pattern, false otherwise
 */
function is_alternating_pattern($phone) {
    $digits = str_split($phone);

    // Check for simple alternating patterns (ABABABAB...)
    $pattern1 = $digits[0] . $digits[1];

    $reconstructed = '';
    for ($i = 0; $i < 10; $i += 2) {
        $reconstructed .= $pattern1;
    }

    if (substr($reconstructed, 0, 10) === $phone) {
        return true;
    }

    // Check for three-digit alternating patterns (ABCABCABC...)
    if (strlen($phone) >= 9) {
        $pattern3 = substr($phone, 0, 3);
        $reconstructed3 = str_repeat($pattern3, 4);
        if (substr($reconstructed3, 0, 10) === $phone) {
            return true;
        }
    }

    return false;
}

/**
 * Check if phone number has valid Indian mobile prefix
 *
 * @param string $phone 10-digit phone number
 * @return bool True if valid prefix, false otherwise
 */
function has_valid_indian_prefix($phone) {
    // Valid Indian mobile number prefixes (first 4 digits)
    $valid_prefixes = [
        // Airtel
        '6000', '6001', '6002', '6003', '6004', '6005', '6006', '6007', '6008', '6009',
        '6200', '6201', '6202', '6203', '6204', '6205', '6206', '6207', '6208', '6209',
        '6300', '6301', '6302', '6303', '6304', '6305', '6306', '6307', '6308', '6309',
        '6400', '6401', '6402', '6403', '6404', '6405', '6406', '6407', '6408', '6409',
        '6500', '6501', '6502', '6503', '6504', '6505', '6506', '6507', '6508', '6509',
        '9000', '9001', '9002', '9003', '9004', '9005', '9006', '9007', '9008', '9009',
        '9100', '9101', '9102', '9103', '9104', '9105', '9106', '9107', '9108', '9109',
        '9200', '9201', '9202', '9203', '9204', '9205', '9206', '9207', '9208', '9209',
        '9300', '9301', '9302', '9303', '9304', '9305', '9306', '9307', '9308', '9309',
        '9800', '9801', '9802', '9803', '9804', '9805', '9806', '9807', '9808', '9809',
        '9900', '9901', '9902', '9903', '9904', '9905', '9906', '9907', '9908', '9909',

        // Jio
        '7000', '7001', '7002', '7003', '7004', '7005', '7006', '7007', '7008', '7009',
        '7400', '7401', '7402', '7403', '7404', '7405', '7406', '7407', '7408', '7409',
        '7900', '7901', '7902', '7903', '7904', '7905', '7906', '7907', '7908', '7909',
        '8700', '8701', '8702', '8703', '8704', '8705', '8706', '8707', '8708', '8709',
        '8800', '8801', '8802', '8803', '8804', '8805', '8806', '8807', '8808', '8809',
        '8900', '8901', '8902', '8903', '8904', '8905', '8906', '8907', '8908', '8909',

        // Vodafone Idea
        '7100', '7101', '7102', '7103', '7104', '7105', '7106', '7107', '7108', '7109',
        '7200', '7201', '7202', '7203', '7204', '7205', '7206', '7207', '7208', '7209',
        '7300', '7301', '7302', '7303', '7304', '7305', '7306', '7307', '7308', '7309',
        '7500', '7501', '7502', '7503', '7504', '7505', '7506', '7507', '7508', '7509',
        '7600', '7601', '7602', '7603', '7604', '7605', '7606', '7607', '7608', '7609',
        '7700', '7701', '7702', '7703', '7704', '7705', '7706', '7707', '7708', '7709',
        '7800', '7801', '7802', '7803', '7804', '7805', '7806', '7807', '7808', '7809',
        '8400', '8401', '8402', '8403', '8404', '8405', '8406', '8407', '8408', '8409',
        '8500', '8501', '8502', '8503', '8504', '8505', '8506', '8507', '8508', '8509',
        '8600', '8601', '8602', '8603', '8604', '8605', '8606', '8607', '8608', '8609',
        '9600', '9601', '9602', '9603', '9604', '9605', '9606', '9607', '9608', '9609',
        '9700', '9701', '9702', '9703', '9704', '9705', '9706', '9707', '9708', '9709',

        // BSNL
        '9400', '9401', '9402', '9403', '9404', '9405', '9406', '9407', '9408', '9409',
        '9500', '9501', '9502', '9503', '9504', '9505', '9506', '9507', '9508', '9509'
    ];

    $prefix = substr($phone, 0, 4);

    // Check if the prefix is in our valid list
    if (in_array($prefix, $valid_prefixes)) {
        return true;
    }

    // Fallback: Check if it starts with valid first two digits for Indian mobile
    $first_two = substr($phone, 0, 2);
    $valid_first_two = ['60', '62', '63', '64', '65', '70', '71', '72', '73', '74', '75', '76', '77', '78', '79', '84', '85', '86', '87', '88', '89', '90', '91', '92', '93', '94', '95', '96', '97', '98', '99'];

    return in_array($first_two, $valid_first_two);
}

/**
 * Get detailed validation error message for phone number
 *
 * @param string $phone Phone number to validate
 * @return array Array with 'valid' boolean and 'message' string
 */
function get_phone_validation_result($phone) {
    $clean_phone = preg_replace('/[^0-9]/', '', $phone);

    if (strlen($clean_phone) !== 10) {
        return [
            'valid' => false,
            'message' => 'Phone number must be exactly 10 digits'
        ];
    }

    if (!preg_match('/^[6-9]/', $clean_phone)) {
        return [
            'valid' => false,
            'message' => 'Indian mobile numbers must start with 6, 7, 8, or 9'
        ];
    }

    if (preg_match('/^(\d)\1{9}$/', $clean_phone)) {
        return [
            'valid' => false,
            'message' => 'Phone number cannot have all same digits'
        ];
    }

    if (is_ascending_sequence($clean_phone)) {
        return [
            'valid' => false,
            'message' => 'Phone number cannot be an ascending sequence'
        ];
    }

    if (is_descending_sequence($clean_phone)) {
        return [
            'valid' => false,
            'message' => 'Phone number cannot be a descending sequence'
        ];
    }

    if (is_alternating_pattern($clean_phone)) {
        return [
            'valid' => false,
            'message' => 'Phone number cannot have alternating patterns'
        ];
    }

    if (preg_match('/(\d)\1{3,}/', $clean_phone)) {
        return [
            'valid' => false,
            'message' => 'Phone number cannot have more than 3 consecutive same digits'
        ];
    }

    if (!has_valid_indian_prefix($clean_phone)) {
        return [
            'valid' => false,
            'message' => 'Invalid Indian mobile number prefix'
        ];
    }

    // Check for specific dummy numbers
    $dummy_numbers = [
        '1234567890', '0123456789', '9876543210', '0987654321',
        '1010101010', '2020202020', '1212121212', '1122334455'
    ];

    if (in_array($clean_phone, $dummy_numbers)) {
        return [
            'valid' => false,
            'message' => 'This appears to be a test or dummy number'
        ];
    }

    return [
        'valid' => true,
        'message' => 'Valid mobile number'
    ];
}

function validate_phone_with_country_code($phone) {
    // Remove any non-digit characters except the plus sign
    $clean_phone = preg_replace('/[^0-9+]/', '', $phone);
    
    if (substr($clean_phone, 0, 1) === '+') {
        return preg_match('/^\+[0-9]{1,3}[0-9]{7,15}$/', $clean_phone);
    } else {
        return preg_match('/^[0-9]{10}$/', $clean_phone);
    }
}

function validate_email($email) {
    // For admin dashboard user page, use the specific regex pattern
    if (basename($_SERVER['SCRIPT_NAME']) === 'users.php' || 
        basename($_SERVER['SCRIPT_NAME']) === 'edit_user.php' || 
        basename(dirname($_SERVER['SCRIPT_NAME'])) . '/' . basename($_SERVER['SCRIPT_NAME']) === 'auth/register.php') {
        return preg_match('/^(?![.])([a-zA-Z0-9_%+-]+(?:\.[a-zA-Z0-9_%+-]+))@(?=.{1,255}$)(?!-)[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)\.[a-zA-Z]{2,}$/', $email);
    }
    
    return true;
}

function validate_aadhar($aadhar) {
    return preg_match('/^\d{12}$/', $aadhar);
}

function validate_pan($pan) {
    return preg_match('/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/', $pan);
}

function validate_password($password) {
    $basic_requirements = preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?]).{6,}$/', $password);
    
    $has_consecutive_digits = preg_match('/\d{4,}/', $password);

    return $basic_requirements && !$has_consecutive_digits;
}

function check_password_strength($password) {
    $score = 0;
    $feedback = '';
    
    // Check for 4 or more consecutive digits
    if (preg_match('/\d{4,}/', $password)) {
        return ['score' => 0, 'feedback' => 'Password cannot contain 4 or more consecutive digits'];
    }
    
    // Length check
    if (strlen($password) < 6) {
        return ['score' => 0, 'feedback' => 'Password is too short'];
    } else if (strlen($password) >= 10) {
        $score += 1;
    }
    
    // Complexity checks
    if (preg_match('/[a-z]/', $password)) $score += 0.5;
    if (preg_match('/[A-Z]/', $password)) $score += 0.5;
    if (preg_match('/\d/', $password)) $score += 1;
    if (preg_match('/[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?]/', $password)) $score += 1;
    
    // Variety check
    $unique_chars = count(array_unique(str_split($password)));
    if ($unique_chars > 7) $score += 1;
    
    $score = min(4, round($score));

    switch ($score) {
        case 0:
            $feedback = 'Very weak password';
            break;
        case 1:
            $feedback = 'Weak password - add uppercase, numbers and special characters';
            break;
        case 2:
            $feedback = 'Fair password - consider adding special characters';
            break;
        case 3:
            $feedback = 'Good password';
            break;
        case 4:
            $feedback = 'Strong password';
            break;
    }
    
    return ['score' => $score, 'feedback' => $feedback];
}
 
function validate_numeric($value) {
    return preg_match('/^\d*\.?\d+$/', $value);
}

function sanitize_and_validate($data, $type = 'text') {

    $sanitized = trim($data);
    $sanitized = stripslashes($sanitized);
    $sanitized = htmlspecialchars($sanitized);

    $valid = true;
    switch ($type) {
        case 'name':
            $valid = validate_name($sanitized);
            break;
        case 'email':
            $valid = validate_email($sanitized);
            break;
        case 'phone':
            $valid = validate_phone($sanitized);
            break;
        case 'aadhar':
            $valid = validate_aadhar($sanitized);
            break;
        case 'pan':
            $valid = validate_pan($sanitized);
            break;
        case 'password':
            $valid = validate_password($sanitized);
            break;
        case 'numeric':
            $valid = validate_numeric($sanitized);
            break;
        default:
            break;
    }
    
    return [
        'value' => $sanitized,
        'valid' => $valid
    ];
}
