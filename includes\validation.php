<?php
function validate_name($name) {
    // Only allow alphabetic characters and spaces
    return preg_match('/^[A-Za-z\s]+$/', $name);
}

function validate_phone($phone) {
    // Remove any non-digit characters
    $clean_phone = preg_replace('/[^0-9]/', '', $phone);
    
    // Check if it's exactly 10 digits
    if (strlen($clean_phone) === 10) {
        // Check for repeating digits (e.g., 0000000000, 9999999999)
        if (preg_match('/^(\d)\1{9}$/', $clean_phone)) {
            return false; // All digits are the same
        }
        return true;
    }
    
    return false;
}

function validate_phone_with_country_code($phone) {
    // Remove any non-digit characters except the plus sign
    $clean_phone = preg_replace('/[^0-9+]/', '', $phone);
    
    if (substr($clean_phone, 0, 1) === '+') {
        return preg_match('/^\+[0-9]{1,3}[0-9]{7,15}$/', $clean_phone);
    } else {
        return preg_match('/^[0-9]{10}$/', $clean_phone);
    }
}

function validate_email($email) {
    // For admin dashboard user page, use the specific regex pattern
    if (basename($_SERVER['SCRIPT_NAME']) === 'users.php' || 
        basename($_SERVER['SCRIPT_NAME']) === 'edit_user.php' || 
        basename(dirname($_SERVER['SCRIPT_NAME'])) . '/' . basename($_SERVER['SCRIPT_NAME']) === 'auth/register.php') {
        return preg_match('/^(?![.])([a-zA-Z0-9_%+-]+(?:\.[a-zA-Z0-9_%+-]+))@(?=.{1,255}$)(?!-)[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)\.[a-zA-Z]{2,}$/', $email);
    }
    
    return true;
}

function validate_aadhar($aadhar) {
    return preg_match('/^\d{12}$/', $aadhar);
}

function validate_pan($pan) {
    return preg_match('/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/', $pan);
}

function validate_password($password) {
    $basic_requirements = preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?]).{6,}$/', $password);
    
    $has_consecutive_digits = preg_match('/\d{4,}/', $password);

    return $basic_requirements && !$has_consecutive_digits;
}

function check_password_strength($password) {
    $score = 0;
    $feedback = '';
    
    // Check for 4 or more consecutive digits
    if (preg_match('/\d{4,}/', $password)) {
        return ['score' => 0, 'feedback' => 'Password cannot contain 4 or more consecutive digits'];
    }
    
    // Length check
    if (strlen($password) < 6) {
        return ['score' => 0, 'feedback' => 'Password is too short'];
    } else if (strlen($password) >= 10) {
        $score += 1;
    }
    
    // Complexity checks
    if (preg_match('/[a-z]/', $password)) $score += 0.5;
    if (preg_match('/[A-Z]/', $password)) $score += 0.5;
    if (preg_match('/\d/', $password)) $score += 1;
    if (preg_match('/[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?]/', $password)) $score += 1;
    
    // Variety check
    $unique_chars = count(array_unique(str_split($password)));
    if ($unique_chars > 7) $score += 1;
    
    $score = min(4, round($score));

    switch ($score) {
        case 0:
            $feedback = 'Very weak password';
            break;
        case 1:
            $feedback = 'Weak password - add uppercase, numbers and special characters';
            break;
        case 2:
            $feedback = 'Fair password - consider adding special characters';
            break;
        case 3:
            $feedback = 'Good password';
            break;
        case 4:
            $feedback = 'Strong password';
            break;
    }
    
    return ['score' => $score, 'feedback' => $feedback];
}
 
function validate_numeric($value) {
    return preg_match('/^\d*\.?\d+$/', $value);
}

function sanitize_and_validate($data, $type = 'text') {

    $sanitized = trim($data);
    $sanitized = stripslashes($sanitized);
    $sanitized = htmlspecialchars($sanitized);

    $valid = true;
    switch ($type) {
        case 'name':
            $valid = validate_name($sanitized);
            break;
        case 'email':
            $valid = validate_email($sanitized);
            break;
        case 'phone':
            $valid = validate_phone($sanitized);
            break;
        case 'aadhar':
            $valid = validate_aadhar($sanitized);
            break;
        case 'pan':
            $valid = validate_pan($sanitized);
            break;
        case 'password':
            $valid = validate_password($sanitized);
            break;
        case 'numeric':
            $valid = validate_numeric($sanitized);
            break;
        default:
            break;
    }
    
    return [
        'value' => $sanitized,
        'valid' => $valid
    ];
}
