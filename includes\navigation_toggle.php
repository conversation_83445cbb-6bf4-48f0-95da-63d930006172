<!-- Inline navigation toggle script for immediate execution -->
<script>
// Immediate execution for menu toggle functionality
(function() {
    // Get the toggle button and sidebar
    const toggleBtn = document.getElementById('menu-toggle');
    const sidebar = document.getElementById('sidebar');
    const closeButtons = document.querySelectorAll('#close-menu');
    
    // Check if elements exist
    if (!toggleBtn || !sidebar) {
        console.error('Menu elements not found in inline script');
        return;
    }
    
    // Toggle menu function
    function toggleMenu() {
        if (sidebar.classList.contains('-translate-x-full')) {
            sidebar.classList.remove('-translate-x-full');
        } else {
            sidebar.classList.add('-translate-x-full');
        }
    }
    
    // Add click event to toggle button
    toggleBtn.onclick = function(e) {
        e.preventDefault();
        toggleMenu();
    };
    
    // Add click events to all close buttons
    closeButtons.forEach(function(btn) {
        btn.onclick = function(e) {
            e.preventDefault();
            toggleMenu();
        };
    });
})();
</script>
