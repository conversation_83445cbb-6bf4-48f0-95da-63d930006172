
<?php
// Determine the correct path to the JS files based on the current script location
$script_path = $_SERVER['SCRIPT_NAME'];
$is_in_subfolder = strpos($script_path, '/auth/') !== false || strpos($script_path, '/includes/') !== false;
$js_base_path = $is_in_subfolder ? '../js/' : 'js/';
?>
<script src="<?php echo $js_base_path; ?>form-validation.js"></script>
<script src="<?php echo $js_base_path; ?>document-validation.js"></script>
<script src="<?php echo $js_base_path; ?>navigation-menu.js"></script>
<script src="<?php echo $js_base_path; ?>menu-fix.js"></script>
<script>
    // Initialize form validation for all forms with the 'needs-validation' class
    document.addEventListener('DOMContentLoaded', function() {
        // Check if there are any validation errors on page load
        const errorElements = document.querySelectorAll('.text-red-600, .border-red-500, .validation-error');
        if (errorElements.length > 0) {
            scrollToFirstError();
        }
        
        // Initialize validation for all forms with the 'needs-validation' class
        const forms = document.querySelectorAll('form.needs-validation');
        forms.forEach(form => {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    scrollToFirstError();
                    return false;
                }
                return true;
            });
        });
    });
</script>
