<?php
/**
 * Migration script to create leads system
 * This creates leads table, notifications table, and updates the system
 */

// Include database configuration
$conn = require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if the user is logged in and has admin role
ensure_session_started();
require_role("admin");

echo "<h1>Create Leads Management System</h1>";
echo "<p>This will create the leads table, notifications system, and update the frontend form.</p>";

// Function to execute SQL with error handling
function executeSqlWithLog($conn, $sql, $description) {
    echo "<p><strong>$description...</strong></p>";
    if (mysqli_query($conn, $sql)) {
        echo "<p style='color: green;'>✓ $description completed successfully.</p>";
        return true;
    } else {
        echo "<p style='color: red;'>✗ Error in $description: " . mysqli_error($conn) . "</p>";
        return false;
    }
}

// Create leads table
$leads_table_sql = "
CREATE TABLE IF NOT EXISTS leads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(15) NOT NULL,
    country_code VARCHAR(5) DEFAULT '+91',
    source VARCHAR(50) DEFAULT 'website',
    address TEXT,
    customer_interest TEXT,
    aadhar_card VARCHAR(12),
    pan_card VARCHAR(10),
    category VARCHAR(50) DEFAULT 'Regular',
    status ENUM('new', 'contacted', 'qualified', 'lost', 'converted') DEFAULT 'new',
    assigned_to INT,
    created_by INT DEFAULT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (assigned_to) REFERENCES employees(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES employees(id) ON DELETE SET NULL,
    INDEX idx_status (status),
    INDEX idx_assigned_to (assigned_to),
    INDEX idx_created_at (created_at)
)";

// Create notifications table
$notifications_table_sql = "
CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('lead', 'sale', 'contact', 'system') DEFAULT 'system',
    related_id INT DEFAULT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES employees(id) ON DELETE CASCADE,
    INDEX idx_user_read (user_id, is_read),
    INDEX idx_created_at (created_at)
)";

// Create lead_conversion_log table to track conversions
$conversion_log_sql = "
CREATE TABLE IF NOT EXISTS lead_conversion_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    lead_id INT NOT NULL,
    contact_id INT NOT NULL,
    converted_by INT NOT NULL,
    conversion_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    FOREIGN KEY (lead_id) REFERENCES leads(id) ON DELETE CASCADE,
    FOREIGN KEY (contact_id) REFERENCES contacts(id) ON DELETE CASCADE,
    FOREIGN KEY (converted_by) REFERENCES employees(id) ON DELETE CASCADE
)";

echo "<h2>Creating Database Tables:</h2>";

$migrations = [
    $leads_table_sql => "Create leads table",
    $notifications_table_sql => "Create notifications table", 
    $conversion_log_sql => "Create lead conversion log table"
];

$success_count = 0;
foreach ($migrations as $sql => $description) {
    if (executeSqlWithLog($conn, $sql, $description)) {
        $success_count++;
    }
}

// Add some sample lead statuses if table was created successfully
if ($success_count > 0) {
    echo "<h2>Setting up initial data:</h2>";
    
    // Check if we need to add any initial data
    $check_leads = mysqli_query($conn, "SELECT COUNT(*) as count FROM leads");
    $lead_count = mysqli_fetch_assoc($check_leads)['count'];
    
    if ($lead_count == 0) {
        echo "<p>Leads table is empty - ready for new leads from frontend form.</p>";
    } else {
        echo "<p>Found $lead_count existing leads in the system.</p>";
    }
}

// Summary
echo "<h2>Migration Summary:</h2>";
if ($success_count == count($migrations)) {
    echo "<p style='color: green; font-weight: bold;'>✓ All migrations completed successfully!</p>";
    echo "<div style='background-color: #f0f9ff; border: 1px solid #0ea5e9; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3 style='color: #0369a1; margin-top: 0;'>✅ System Ready!</h3>";
    echo "<ul style='color: #0369a1;'>";
    echo "<li><strong>Leads Table:</strong> Created to store frontend form submissions</li>";
    echo "<li><strong>Notifications Table:</strong> Created for real-time notifications</li>";
    echo "<li><strong>Conversion Log:</strong> Created to track lead-to-customer conversions</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>Next Steps:</h3>";
    echo "<ol>";
    echo "<li>Create the leads management page</li>";
    echo "<li>Update frontend contact form to submit to leads</li>";
    echo "<li>Set up email notifications</li>";
    echo "<li>Add real-time notification system</li>";
    echo "<li>Create automatic conversion logic</li>";
    echo "</ol>";
} else {
    echo "<p style='color: red; font-weight: bold;'>✗ Some migrations failed. Please check the errors above.</p>";
}

echo "<div style='margin-top: 20px;'>";
echo "<a href='admin.php' style='background-color: #0b192c; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Back to Admin</a>";
echo "</div>";
?>
