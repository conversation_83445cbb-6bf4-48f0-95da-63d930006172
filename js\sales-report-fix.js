/**
 * Sales Report Date Filtering Fix
 * 
 * This script fixes the issue with date filtering in the sales report generation.
 * It ensures that the correct date range is used when generating reports.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get the report generation button
    const generateReportBtn = document.getElementById('generate-report-btn');
    
    if (generateReportBtn) {
        // Override the default click handler
        generateReportBtn.addEventListener('click', function(e) {
            e.preventDefault();
            generateSalesReportFixed();
        });
    }
    
    // Initialize with default date range (last 30 days)
    const fromDateInput = document.getElementById('report-date-from');
    const toDateInput = document.getElementById('report-date-to');
    
    if (fromDateInput && toDateInput) {
        // Set default date range to last 30 days
        const today = new Date();
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(today.getDate() - 30);
        
        // Format dates as YYYY-MM-DD
        const formatDate = (date) => {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        };
        
        fromDateInput.value = formatDate(thirtyDaysAgo);
        toDateInput.value = formatDate(today);
    }
    
    // Generate initial report
    setTimeout(generateSalesReportFixed, 500);
});

/**
 * Fixed version of the sales report generation function
 * This ensures proper date formatting and validation
 * and prevents page reload when generating reports
 */
function generateSalesReportFixed() {
    console.log('Generating sales report with fixed date handling...');
    
    // Get date inputs
    const fromDateInput = document.getElementById('report-date-from');
    const toDateInput = document.getElementById('report-date-to');
    
    if (!fromDateInput || !toDateInput) {
        console.error('Date input elements not found');
        return;
    }
    
    // Get date values
    const fromDate = fromDateInput.value;
    const toDate = toDateInput.value;
    
    console.log('Date range:', fromDate, 'to', toDate);
    
    // Validate dates
    if (!fromDate || !toDate) {
        alert('Please select both from and to dates');
        console.error('Missing date values');
        return;
    }
    
    const fromDateObj = new Date(fromDate);
    const toDateObj = new Date(toDate);
    
    if (fromDateObj > toDateObj) {
        alert('From date cannot be after to date');
        console.error('Invalid date range: from date is after to date');
        return;
    }
    
    // Format dates for display
    const fromDateFormatted = fromDateObj.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
    const toDateFormatted = toDateObj.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
    
    // Update the date display
    const dateDisplay = document.getElementById('report-date-display');
    if (dateDisplay) {
        dateDisplay.textContent = `${fromDateFormatted} to ${toDateFormatted}`;
    }
    
    // Format dates for API calls (YYYY-MM-DD)
    const formatDateForAPI = (date) => {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };
    
    const fromDateAPI = formatDateForAPI(fromDate);
    const toDateAPI = formatDateForAPI(toDate);
    
    console.log('Formatted API dates:', fromDateAPI, 'to', toDateAPI);
    
    // Show loading indicator
    const reportSection = document.querySelector('.sales-report-section');
    if (reportSection) {
        const loadingIndicator = document.createElement('div');
        loadingIndicator.id = 'report-loading';
        loadingIndicator.className = 'text-center py-4';
        loadingIndicator.innerHTML = '<div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div><p class="mt-2">Loading report data...</p>';
        
        // Clear previous results and show loading
        const resultsContainer = document.getElementById('report-results-container');
        if (resultsContainer) {
            resultsContainer.innerHTML = '';
            resultsContainer.appendChild(loadingIndicator);
        }
    }
    
    // Fetch data with properly formatted dates using AJAX instead of page reload
    fetchMonthlySalesData(fromDateAPI, toDateAPI);
    fetchProductSalesData(fromDateAPI, toDateAPI);
    fetchSalesSummary(fromDateAPI, toDateAPI);
    fetchTopCustomers(fromDateAPI, toDateAPI);
    fetchTopExecutives(fromDateAPI, toDateAPI);
    
    // Show the report section if it's hidden
    if (reportSection && reportSection.style.display === 'none') {
        reportSection.style.display = 'block';
    }
    
    // Scroll to the report section
    setTimeout(() => {
        const reportSection = document.querySelector('.sales-report-section');
        if (reportSection) {
            reportSection.scrollIntoView({ behavior: 'smooth' });
        }
    }, 500);
}
