<?php
// Get database connection using reliable method
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';

// Check if database is set up first
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();

// Check if the user is logged in
ensure_session_started();
require_login();

// Block access for sales reps
if (isset($_SESSION['role']) && $_SESSION['role'] === 'sales_rep') {
    header("Location: contacts.php?error=Unauthorized");
    exit;
}

// Get contact ID from URL
$contact_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($contact_id <= 0) {
    header("Location: contacts.php");
    exit;
}

$error_message = '';
$success_message = '';

// Get contact data
$sql = "SELECT * FROM contacts WHERE id = ?";
$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, "i", $contact_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    header("Location: contacts.php?error=Contact not found");
    exit;
}

$contact = mysqli_fetch_assoc($result);

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $first_name = sanitize_input($_POST['first_name']);
    $last_name = sanitize_input($_POST['last_name']);
    $email = sanitize_input($_POST['email']);
    $phone = sanitize_input($_POST['phone']);
    $assigned_to = !empty($_POST['assigned_to']) ? intval($_POST['assigned_to']) : null;
    $address = sanitize_input($_POST['address']);
    $aadhar_card = sanitize_input($_POST['aadhar_card']);
    $pan_card = sanitize_input($_POST['pan_card']);
    $category = sanitize_input($_POST['category']);
    $product_interests = isset($_POST['product_interests']) ? $_POST['product_interests'] : [];

    // Convert selected product interests to comma-separated string for customer_interest field
    if (!empty($product_interests)) {
        // Get product names for the selected IDs
        $product_names = [];
        $placeholders = str_repeat('?,', count($product_interests) - 1) . '?';
        $product_query = "SELECT name FROM products WHERE id IN ($placeholders)";
        $product_stmt = mysqli_prepare($conn, $product_query);

        // Create types string (all integers)
        $types = str_repeat('i', count($product_interests));
        mysqli_stmt_bind_param($product_stmt, $types, ...$product_interests);
        mysqli_stmt_execute($product_stmt);
        $product_result = mysqli_stmt_get_result($product_stmt);

        while ($row = mysqli_fetch_assoc($product_result)) {
            $product_names[] = $row['name'];
        }

        $customer_interest = implode(', ', $product_names);
    } else {
        $customer_interest = '';
    }
    
    // Validation
    $errors = [];
    
    if (empty($first_name)) {
        $errors[] = "First name is required.";
    }
    
    if (empty($last_name)) {
        $errors[] = "Last name is required.";
    }
    
    if (empty($email)) {
        $errors[] = "Email is required.";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Please enter a valid email address.";
    }
    
    // Check if email already exists (excluding current contact)
    if (!empty($email)) {
        $check_email_sql = "SELECT id FROM contacts WHERE email = ? AND id != ?";
        $check_email_stmt = mysqli_prepare($conn, $check_email_sql);
        mysqli_stmt_bind_param($check_email_stmt, "si", $email, $contact_id);
        mysqli_stmt_execute($check_email_stmt);
        $check_email_result = mysqli_stmt_get_result($check_email_stmt);
        
        if (mysqli_num_rows($check_email_result) > 0) {
            $errors[] = "A contact with this email already exists.";
        }
    }
    
    if (empty($errors)) {
        $sql = "UPDATE contacts SET
                first_name = ?, last_name = ?, email = ?, phone = ?,
                assigned_to = ?, address = ?,
                aadhar_card = ?, pan_card = ?, customer_interest = ?, category = ?,
                updated_at = CURRENT_TIMESTAMP
                WHERE id = ?";

        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "ssssisssssi",
            $first_name, $last_name, $email, $phone,
            $assigned_to, $address,
            $aadhar_card, $pan_card, $customer_interest, $category, $contact_id);
        
        if (mysqli_stmt_execute($stmt)) {
            // Product interests are now saved in the customer_interest field
            header("Location: view_contact.php?id=" . $contact_id . "&success=updated");
            exit;
        } else {
            $error_message = "Error updating contact: " . mysqli_error($conn);
        }
    } else {
        $error_message = implode("<br>", $errors);
    }
} else {
    // Pre-populate form with existing data
    $_POST = $contact;
}

// Get only sales representatives for assignment dropdown
$users_query = "SELECT id, first_name, last_name FROM employees WHERE role = 'sales_rep' ORDER BY first_name, last_name";
$users_result = mysqli_query($conn, $users_query);

// Get all products for interest selection
$products_query = "SELECT id, name, category FROM products ORDER BY category, name";
$products_result = mysqli_query($conn, $products_query);

// Check if contact_product_interests table exists
$table_check = "SHOW TABLES LIKE 'contact_product_interests'";
$table_result = mysqli_query($conn, $table_check);
$interests_table_exists = mysqli_num_rows($table_result) > 0;

// Get all products for interest selection
$products_query = "SELECT id, name, category FROM products ORDER BY category, name";
$products_result = mysqli_query($conn, $products_query);

// Get current product interests from customer_interest field
$current_product_interests = [];
if (!empty($contact['customer_interest'])) {
    // Parse the customer_interest field to find matching product IDs
    $interest_names = array_map('trim', explode(',', $contact['customer_interest']));

    // Get product IDs for the interest names
    if (!empty($interest_names)) {
        $placeholders = str_repeat('?,', count($interest_names) - 1) . '?';
        $match_query = "SELECT id FROM products WHERE name IN ($placeholders)";
        $match_stmt = mysqli_prepare($conn, $match_query);

        $types = str_repeat('s', count($interest_names));
        mysqli_stmt_bind_param($match_stmt, $types, ...$interest_names);
        mysqli_stmt_execute($match_stmt);
        $match_result = mysqli_stmt_get_result($match_stmt);

        while ($row = mysqli_fetch_assoc($match_result)) {
            $current_product_interests[] = $row['id'];
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title>Edit Contact - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .text-color { color: #FF6500; }
        .bg-color { background-color: #FF6500; }
    </style>
</head>
<body class="bg-gray-100">

<?php include 'includes/navigation.php'; ?>

<!-- Main Content -->
<div class="ml-0 min-[870px]:ml-60 min-h-screen flex flex-col">
    <header class="bg-[#1E3E62] p-4 flex justify-between z-10 flex-col">
        <h1 class="text-2xl font-bold text-white mx-10 my-2">Edit Contact</h1>
            </header>
    
    <main class="flex-1 p-10">
        <?php if (!empty($error_message)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <?php echo $error_message; ?>
        </div>
        <?php endif; ?>

        <div class="bg-white rounded-lg shadow p-6">
            <form method="POST" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- First Name -->
                    <div>
                        <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">
                            First Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="first_name" name="first_name" required pattern="[A-Za-z\s]+" 
                               title="Only alphabetic characters are allowed"
                               value="<?php echo htmlspecialchars($_POST['first_name'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <small class="text-gray-500">Only alphabetic characters are allowed</small>
                    </div>

                    <!-- Last Name -->
                    <div>
                        <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">
                            Last Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="last_name" name="last_name" required pattern="[A-Za-z\s]+"
                               title="Only alphabetic characters are allowed"
                               value="<?php echo htmlspecialchars($_POST['last_name'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <small class="text-gray-500">Only alphabetic characters are allowed</small>
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                            Email <span class="text-red-500">*</span>
                        </label>
                        <input type="email" id="email" name="email" required
                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>

                    <!-- Phone -->
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                        <input type="tel" id="phone" name="phone"
                               value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                    </div>



                    


                    <!-- Assigned To -->
                    <div>
                        <label for="assigned_to" class="block text-sm font-medium text-gray-700 mb-2">Assign To</label>
                        <select id="assigned_to" name="assigned_to"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Unassigned</option>
                            <?php while ($user = mysqli_fetch_assoc($users_result)): ?>
                            <option value="<?php echo $user['id']; ?>" <?php echo ($_POST['assigned_to'] ?? '') == $user['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                            </option>
                            <?php endwhile; ?>
                        </select>
                    </div>

                    <!-- Category -->
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                        <input type="text" id="category" name="category"
                               value="<?php echo htmlspecialchars($_POST['category'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>

                    <!-- Aadhar Card -->
                    <div>
                        <label for="aadhar_card" class="block text-sm font-medium text-gray-700 mb-2">Aadhar Card</label>
                        <input type="text" id="aadhar_card" name="aadhar_card"
                               value="<?php echo htmlspecialchars($_POST['aadhar_card'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>

                    <!-- PAN Card -->
                    <div>
                        <label for="pan_card" class="block text-sm font-medium text-gray-700 mb-2">PAN Card</label>
                        <input type="text" id="pan_card" name="pan_card"
                               value="<?php echo htmlspecialchars($_POST['pan_card'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                </div>

                <!-- Address -->
                <div>
                    <label for="address" class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                    <textarea id="address" name="address" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="Full address..."><?php echo htmlspecialchars($_POST['address'] ?? ''); ?></textarea>
                </div>

                <!-- Product Interests -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Product Interests</label>
                    <div class="border border-gray-300 rounded-md p-3 max-h-48 overflow-y-auto">
                        <?php
                        mysqli_data_seek($products_result, 0); // Reset result pointer
                        $current_category = '';
                        while ($product = mysqli_fetch_assoc($products_result)):
                            if ($current_category !== $product['category']):
                                if ($current_category !== '') echo '</div>';
                                $current_category = $product['category'];
                        ?>
                            <div class="mb-3">
                                <h4 class="text-sm font-medium text-gray-600 mb-2"><?php echo htmlspecialchars($current_category ?: 'General'); ?></h4>
                        <?php endif; ?>
                                <label class="flex items-center mb-1">
                                    <input type="checkbox" name="product_interests[]" value="<?php echo $product['id']; ?>"
                                           <?php echo in_array($product['id'], $current_product_interests) ? 'checked' : ''; ?>
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                                    <span class="text-sm text-gray-700"><?php echo htmlspecialchars($product['name']); ?></span>
                                </label>
                        <?php endwhile; ?>
                        <?php if ($current_category !== '') echo '</div>'; ?>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">Select products the contact is interested in</p>
                </div>

                <!-- Customer Interest (Legacy) -->
                <div>
                    <label for="customer_interest" class="block text-sm font-medium text-gray-700 mb-2">Additional Interest Notes</label>
                    <textarea id="customer_interest" name="customer_interest" rows="2"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="Any additional notes about customer interests..."><?php echo htmlspecialchars($_POST['customer_interest'] ?? ''); ?></textarea>
                </div>



                <!-- Submit Buttons -->
                <div class="flex justify-end space-x-4">
                    <a href="view_contact.php?id=<?php echo $contact_id; ?>" class="px-6 py-2 bg-[#0b192c] text-white rounded-sm hover:bg-[#1e3e62]">
                        Cancel
                    </a>
                    <button type="submit" class="px-6 py-2 bg-[#0b192c] text-white rounded-sm hover:bg-[#1e3e62]" title="Update Contact">
                        <i class="fas fa-save"></i>
                    </button>
                </div>
            </form>
        </div>
    </main>
</div>

</body>
</html>
