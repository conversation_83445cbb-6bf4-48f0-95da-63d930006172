/**
 * LEAD Management System - Form Validation Functions
 * 
 * This file contains all validation functions for form inputs across the application.
 */

// Validate name fields (no numbers or special characters)
function validateName(name) {
    const nameRegex = /^[A-Za-z\s]+$/;
    return nameRegex.test(name);
}

// Validate Indian mobile number with comprehensive checks
function validateIndianMobile(phone) {
    // Remove all non-digit characters
    const cleanPhone = phone.replace(/\D/g, '');

    // Check if it's exactly 10 digits
    if (cleanPhone.length !== 10) {
        return {
            valid: false,
            message: 'Phone number must be exactly 10 digits'
        };
    }

    // Check if it starts with a valid digit (6-9 for Indian mobile numbers)
    if (!/^[6-9]/.test(cleanPhone)) {
        return {
            valid: false,
            message: 'Indian mobile numbers must start with 6, 7, 8, or 9'
        };
    }

    // Check for all same digits
    if (/^(\d)\1{9}$/.test(cleanPhone)) {
        return {
            valid: false,
            message: 'Phone number cannot have all same digits'
        };
    }

    // Check for ascending sequences
    if (isAscendingSequence(cleanPhone)) {
        return {
            valid: false,
            message: 'Phone number cannot be an ascending sequence'
        };
    }

    // Check for descending sequences
    if (isDescendingSequence(cleanPhone)) {
        return {
            valid: false,
            message: 'Phone number cannot be a descending sequence'
        };
    }

    // Check for alternating patterns
    if (isAlternatingPattern(cleanPhone)) {
        return {
            valid: false,
            message: 'Phone number cannot have alternating patterns'
        };
    }

    // Check for too many consecutive same digits
    if (/(\d)\1{3,}/.test(cleanPhone)) {
        return {
            valid: false,
            message: 'Phone number cannot have more than 3 consecutive same digits'
        };
    }

    // Check for dummy numbers
    const dummyNumbers = [
        '0000000000', '1111111111', '2222222222', '3333333333', '4444444444',
        '5555555555', '6666666666', '7777777777', '8888888888', '9999999999',
        '1234567890', '0123456789', '9876543210', '0987654321',
        '1010101010', '2020202020', '1212121212', '1122334455',
        '9123456789', '8123456789', '7123456789', '6123456789'
    ];

    if (dummyNumbers.includes(cleanPhone)) {
        return {
            valid: false,
            message: 'This appears to be a test or dummy number'
        };
    }

    // Check for valid Indian mobile prefix
    if (!hasValidIndianPrefix(cleanPhone)) {
        return {
            valid: false,
            message: 'Invalid Indian mobile number prefix'
        };
    }

    return {
        valid: true,
        message: 'Valid mobile number'
    };
}

// Legacy function for backward compatibility
function validatePhone(phone) {
    const result = validateIndianMobile(phone);
    return result.valid;
}

// Helper function: Check if phone number is an ascending sequence
function isAscendingSequence(phone) {
    const digits = phone.split('').map(Number);
    let ascendingCount = 0;

    for (let i = 1; i < digits.length; i++) {
        const current = digits[i];
        const previous = digits[i - 1];

        // Handle wrap-around (9 to 0)
        if ((current === previous + 1) || (previous === 9 && current === 0)) {
            ascendingCount++;
        } else {
            ascendingCount = 0;
        }

        // If we have 6 or more consecutive ascending digits, it's invalid
        if (ascendingCount >= 6) {
            return true;
        }
    }

    return false;
}

// Helper function: Check if phone number is a descending sequence
function isDescendingSequence(phone) {
    const digits = phone.split('').map(Number);
    let descendingCount = 0;

    for (let i = 1; i < digits.length; i++) {
        const current = digits[i];
        const previous = digits[i - 1];

        // Handle wrap-around (0 to 9)
        if ((current === previous - 1) || (previous === 0 && current === 9)) {
            descendingCount++;
        } else {
            descendingCount = 0;
        }

        // If we have 6 or more consecutive descending digits, it's invalid
        if (descendingCount >= 6) {
            return true;
        }
    }

    return false;
}

// Helper function: Check if phone number has alternating pattern
function isAlternatingPattern(phone) {
    const digits = phone.split('');

    // Check for simple alternating patterns (ABABABAB...)
    const pattern1 = digits[0] + digits[1];

    let reconstructed = '';
    for (let i = 0; i < 10; i += 2) {
        reconstructed += pattern1;
    }

    if (reconstructed.substring(0, 10) === phone) {
        return true;
    }

    // Check for three-digit alternating patterns (ABCABCABC...)
    if (phone.length >= 9) {
        const pattern3 = phone.substring(0, 3);
        const reconstructed3 = pattern3.repeat(4);
        if (reconstructed3.substring(0, 10) === phone) {
            return true;
        }
    }

    return false;
}

// Helper function: Check if phone number has valid Indian mobile prefix
function hasValidIndianPrefix(phone) {
    // Valid Indian mobile number prefixes (first 2 digits)
    const validFirstTwo = ['60', '62', '63', '64', '65', '70', '71', '72', '73', '74', '75', '76', '77', '78', '79', '84', '85', '86', '87', '88', '89', '90', '91', '92', '93', '94', '95', '96', '97', '98', '99'];

    const firstTwo = phone.substring(0, 2);
    return validFirstTwo.includes(firstTwo);
}

// Validate email format
function validateEmail(email) {
    const emailRegex = /^(?![.])([a-zA-Z0-9_%+-]+(?:\.[a-zA-Z0-9_%+-]+))@(?=.{1,255}$)(?!-)[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
}

// Validate Aadhar Card (12 digits)
function validateAadhar(aadhar) {
    const aadharRegex = /^\d{12}$/;
    return aadharRegex.test(aadhar);
}

// Validate PAN Card (10 alphanumeric characters in specific format)
function validatePAN(pan) {
    const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
    return panRegex.test(pan);
}

// Validate password (minimum 6 characters, at least one uppercase, one lowercase, one number)
function validatePassword(password) {
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{6,}$/;
    return passwordRegex.test(password);
}

// Validate numeric input (for quantities, prices, etc.)
function validateNumeric(value) {
    const numericRegex = /^\d*\.?\d+$/;
    return numericRegex.test(value);
}

// Apply validation to name fields
document.addEventListener('DOMContentLoaded', function() {
    // Apply name validation to all name fields
    const nameFields = document.querySelectorAll('input[name="name"], input[name="first_name"], input[name="last_name"], input[name="customer_name"]');
    nameFields.forEach(field => {
        field.addEventListener('input', function() {
            if (this.value && !validateName(this.value)) {
                this.setCustomValidity('Name should contain only letters and spaces');
                this.classList.add('border-red-500');
            } else {
                this.setCustomValidity('');
                this.classList.remove('border-red-500');
            }
        });
    });

    // Apply comprehensive Indian mobile validation to all phone fields (excluding referrer_phone which has custom handling)
    const phoneFields = document.querySelectorAll('input[name="phone"], input[name="customer_phone"]');
    phoneFields.forEach(field => {
        // Skip if field already has custom validation (to prevent duplicate handlers)
        if (field.hasAttribute('data-custom-validation')) {
            return;
        }

        field.addEventListener('input', function() {
            if (this.value) {
                const validation = validateIndianMobile(this.value);
                if (!validation.valid) {
                    this.setCustomValidity(validation.message);
                    this.classList.add('border-red-500');

                    // Show validation message in a nearby element if it exists
                    const messageElement = this.parentElement.querySelector('.phone-validation-message');
                    if (messageElement) {
                        messageElement.textContent = validation.message;
                        messageElement.className = 'phone-validation-message text-red-600 text-sm mt-1';
                    }
                } else {
                    this.setCustomValidity('');
                    this.classList.remove('border-red-500');

                    // Clear validation message
                    const messageElement = this.parentElement.querySelector('.phone-validation-message');
                    if (messageElement) {
                        messageElement.textContent = validation.message;
                        messageElement.className = 'phone-validation-message text-green-600 text-sm mt-1';
                    }
                }
            } else {
                this.setCustomValidity('');
                this.classList.remove('border-red-500');

                // Clear validation message
                const messageElement = this.parentElement.querySelector('.phone-validation-message');
                if (messageElement) {
                    messageElement.textContent = '';
                }
            }
        });

        // Add validation message element if it doesn't exist
        if (!field.parentElement.querySelector('.phone-validation-message')) {
            const messageElement = document.createElement('div');
            messageElement.className = 'phone-validation-message text-sm mt-1';
            field.parentElement.appendChild(messageElement);
        }
    });

    // Email validation has been removed as requested

    // Apply Aadhar validation
    const aadharFields = document.querySelectorAll('input[name="aadhar_card"], input[name="aadhar_id"]');
    aadharFields.forEach(field => {
        field.addEventListener('input', function() {
            if (this.value && !validateAadhar(this.value)) {
                this.setCustomValidity('Aadhar number must be exactly 12 digits');
                this.classList.add('border-red-500');
            } else {
                this.setCustomValidity('');
                this.classList.remove('border-red-500');
            }
        });
    });

    // Apply PAN validation
    const panFields = document.querySelectorAll('input[name="pan_card"]');
    panFields.forEach(field => {
        field.addEventListener('input', function() {
            if (this.value && !validatePAN(this.value)) {
                this.setCustomValidity('PAN must be in format **********');
                this.classList.add('border-red-500');
            } else {
                this.setCustomValidity('');
                this.classList.remove('border-red-500');
            }
        });
    });

    // Apply password validation
    const passwordFields = document.querySelectorAll('input[type="password"]');
    passwordFields.forEach(field => {
        if (field.name === 'password') {
            field.addEventListener('input', function() {
                if (this.value && !validatePassword(this.value)) {
                    this.setCustomValidity('Password must be at least 6 characters with at least one uppercase letter, one lowercase letter, and one number');
                    this.classList.add('border-red-500');
                } else {
                    this.setCustomValidity('');
                    this.classList.remove('border-red-500');
                }
            });
        }
    });

    // Apply numeric validation to quantity and price fields
    const numericFields = document.querySelectorAll('input[name="quantity[]"], input[name="price[]"], input[name="subtotal"], input[name="tax"], input[name="total_amount"]');
    numericFields.forEach(field => {
        field.addEventListener('input', function() {
            if (this.value && !validateNumeric(this.value)) {
                this.setCustomValidity('Please enter a valid number');
                this.classList.add('border-red-500');
            } else {
                this.setCustomValidity('');
                this.classList.remove('border-red-500');
            }
        });
    });
});
