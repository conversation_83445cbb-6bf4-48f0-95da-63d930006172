/**
 * LEAD Management System - Form Validation Functions
 * 
 * This file contains all validation functions for form inputs across the application.
 */

// Validate name fields (no numbers or special characters)
function validateName(name) {
    const nameRegex = /^[A-Za-z\s]+$/;
    return nameRegex.test(name);
}

// Validate phone number (10 digits only)
function validatePhone(phone) {
    const phoneRegex = /^\d{10}$/;
    return phoneRegex.test(phone);
}

// Validate email format
function validateEmail(email) {
    const emailRegex = /^(?![.])([a-zA-Z0-9_%+-]+(?:\.[a-zA-Z0-9_%+-]+))@(?=.{1,255}$)(?!-)[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
}

// Validate Aadhar Card (12 digits)
function validateAadhar(aadhar) {
    const aadharRegex = /^\d{12}$/;
    return aadharRegex.test(aadhar);
}

// Validate PAN Card (10 alphanumeric characters in specific format)
function validatePAN(pan) {
    const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
    return panRegex.test(pan);
}

// Validate password (minimum 6 characters, at least one uppercase, one lowercase, one number)
function validatePassword(password) {
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{6,}$/;
    return passwordRegex.test(password);
}

// Validate numeric input (for quantities, prices, etc.)
function validateNumeric(value) {
    const numericRegex = /^\d*\.?\d+$/;
    return numericRegex.test(value);
}

// Apply validation to name fields
document.addEventListener('DOMContentLoaded', function() {
    // Apply name validation to all name fields
    const nameFields = document.querySelectorAll('input[name="name"], input[name="first_name"], input[name="last_name"], input[name="customer_name"]');
    nameFields.forEach(field => {
        field.addEventListener('input', function() {
            if (this.value && !validateName(this.value)) {
                this.setCustomValidity('Name should contain only letters and spaces');
                this.classList.add('border-red-500');
            } else {
                this.setCustomValidity('');
                this.classList.remove('border-red-500');
            }
        });
    });

    // Apply phone validation to all phone fields
    const phoneFields = document.querySelectorAll('input[name="phone"], input[name="customer_phone"]');
    phoneFields.forEach(field => {
        field.addEventListener('input', function() {
            if (this.value && !validatePhone(this.value)) {
                this.setCustomValidity('Phone number must be exactly 10 digits');
                this.classList.add('border-red-500');
            } else {
                this.setCustomValidity('');
                this.classList.remove('border-red-500');
            }
        });
    });

    // Email validation has been removed as requested

    // Apply Aadhar validation
    const aadharFields = document.querySelectorAll('input[name="aadhar_card"], input[name="aadhar_id"]');
    aadharFields.forEach(field => {
        field.addEventListener('input', function() {
            if (this.value && !validateAadhar(this.value)) {
                this.setCustomValidity('Aadhar number must be exactly 12 digits');
                this.classList.add('border-red-500');
            } else {
                this.setCustomValidity('');
                this.classList.remove('border-red-500');
            }
        });
    });

    // Apply PAN validation
    const panFields = document.querySelectorAll('input[name="pan_card"]');
    panFields.forEach(field => {
        field.addEventListener('input', function() {
            if (this.value && !validatePAN(this.value)) {
                this.setCustomValidity('PAN must be in format **********');
                this.classList.add('border-red-500');
            } else {
                this.setCustomValidity('');
                this.classList.remove('border-red-500');
            }
        });
    });

    // Apply password validation
    const passwordFields = document.querySelectorAll('input[type="password"]');
    passwordFields.forEach(field => {
        if (field.name === 'password') {
            field.addEventListener('input', function() {
                if (this.value && !validatePassword(this.value)) {
                    this.setCustomValidity('Password must be at least 6 characters with at least one uppercase letter, one lowercase letter, and one number');
                    this.classList.add('border-red-500');
                } else {
                    this.setCustomValidity('');
                    this.classList.remove('border-red-500');
                }
            });
        }
    });

    // Apply numeric validation to quantity and price fields
    const numericFields = document.querySelectorAll('input[name="quantity[]"], input[name="price[]"], input[name="subtotal"], input[name="tax"], input[name="total_amount"]');
    numericFields.forEach(field => {
        field.addEventListener('input', function() {
            if (this.value && !validateNumeric(this.value)) {
                this.setCustomValidity('Please enter a valid number');
                this.classList.add('border-red-500');
            } else {
                this.setCustomValidity('');
                this.classList.remove('border-red-500');
            }
        });
    });
});
