/**
 * Form Validation Utilities
 * 
 * This file contains utility functions for form validation and error handling
 */

/**
 * Scrolls to the first error message or invalid field in a form
 * Called automatically when a form has validation errors
 */
function scrollToFirstError() {
    // First try to find error messages
    const errorElements = document.querySelectorAll('.text-red-600, .border-red-500, .validation-error');
    
    if (errorElements.length > 0) {
        // Get the first error element
        const firstError = errorElements[0];
        
        // Scroll to the error with some offset for better visibility
        window.scrollTo({
            top: getOffsetTop(firstError) - 100,
            behavior: 'smooth'
        });
        
        // Try to focus the input field if it's related to the error
        const relatedInput = findRelatedInput(firstError);
        if (relatedInput) {
            setTimeout(() => {
                relatedInput.focus();
            }, 500);
        }
    }
}

/**
 * Gets the offset top position of an element relative to the document
 * 
 * @param {HTMLElement} element - The element to get the offset for
 * @return {number} The offset top position
 */
function getOffsetTop(element) {
    let offsetTop = 0;
    while(element) {
        offsetTop += element.offsetTop;
        element = element.offsetParent;
    }
    return offsetTop;
}

/**
 * Finds the input field related to an error message
 * 
 * @param {HTMLElement} errorElement - The error message element
 * @return {HTMLElement|null} The related input element or null if not found
 */
function findRelatedInput(errorElement) {
    // If the error element is directly after an input, return that input
    const prevElement = errorElement.previousElementSibling;
    if (prevElement && (prevElement.tagName === 'INPUT' || prevElement.tagName === 'SELECT' || prevElement.tagName === 'TEXTAREA')) {
        return prevElement;
    }
    
    // If the error is inside a div that also contains the input
    const parentDiv = errorElement.parentElement;
    if (parentDiv) {
        const input = parentDiv.querySelector('input, select, textarea');
        if (input) {
            return input;
        }
    }
    
    // If the error has a specific id that matches an input's id + "-error"
    if (errorElement.id && errorElement.id.endsWith('-error')) {
        const inputId = errorElement.id.replace('-error', '');
        const input = document.getElementById(inputId);
        if (input) {
            return input;
        }
    }
    
    // If the error element has a data-for attribute pointing to an input
    if (errorElement.dataset.for) {
        const input = document.getElementById(errorElement.dataset.for);
        if (input) {
            return input;
        }
    }
    
    return null;
}

/**
 * Initializes form validation for a specific form
 * 
 * @param {string} formId - The ID of the form to initialize
 */
function initFormValidation(formId) {
    const form = document.getElementById(formId);
    if (!form) return;
    
    form.addEventListener('submit', function(event) {
        // Check HTML5 validation
        if (!form.checkValidity()) {
            event.preventDefault();
            scrollToFirstError();
            return false;
        }
        
        // Additional custom validation can be added here
        return true;
    });
}

// When the DOM is loaded, check if there are any validation errors and scroll to them
document.addEventListener('DOMContentLoaded', function() {
    // Check if there are any validation errors on page load
    const errorElements = document.querySelectorAll('.text-red-600, .border-red-500, .validation-error');
    if (errorElements.length > 0) {
        scrollToFirstError();
    }
    
    // Initialize validation for all forms with the 'needs-validation' class
    const forms = document.querySelectorAll('form.needs-validation');
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                scrollToFirstError();
                return false;
            }
            return true;
        });
    });
});
