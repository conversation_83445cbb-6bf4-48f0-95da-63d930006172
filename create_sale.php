<?php
// Get database connection using reliable method
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';

// Check if database is set up first
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();

// Check if the user is logged in
ensure_session_started();
require_login();

// Check if user is a sales rep
if (!has_role("sales_rep")) {
    header("Location: admin.php");
    exit;
}

// Get the sales rep ID
$sales_rep_id = $_SESSION['user_id'];

// Get all products for selection
$products_query = "SELECT * FROM products ORDER BY name";
$products_result = mysqli_query($conn, $products_query);

// Get contacts that this sales rep can sell to
$customers_query = "SELECT DISTINCT c.id, CONCAT(c.first_name, ' ', c.last_name) as name, c.phone, c.email, c.address
                   FROM contacts c
                   WHERE (c.assigned_to = ? OR c.created_by = ?)
                   ORDER BY name";
$customers_stmt = mysqli_prepare($conn, $customers_query);
mysqli_stmt_bind_param($customers_stmt, "ii", $sales_rep_id, $sales_rep_id);
mysqli_stmt_execute($customers_stmt);
$customers_result = mysqli_stmt_get_result($customers_stmt);

// Generate a unique invoice number
$invoice_number = generate_invoice_number();

// Process form submission for creating a sale
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['action']) && $_POST['action'] == 'create_sale') {
    // Initialize validation errors array
    $validation_errors = [];
    
    // Check if using existing customer or creating a new one
    if (!empty($_POST['customer_id'])) {
        // Using existing customer
        $customer_id = intval($_POST['customer_id']);
        
        // Get contact details from database to ensure data integrity
        $get_contact_query = "SELECT *, CONCAT(first_name, ' ', last_name) as name FROM contacts WHERE id = ?";
        $get_contact_stmt = mysqli_prepare($conn, $get_contact_query);
        mysqli_stmt_bind_param($get_contact_stmt, "i", $customer_id);
        mysqli_stmt_execute($get_contact_stmt);
        $customer_result = mysqli_stmt_get_result($get_contact_stmt);
        
        if (mysqli_num_rows($customer_result) > 0) {
            $customer_data = mysqli_fetch_assoc($customer_result);
            $customer_name = $customer_data['name'];
            $customer_phone = $customer_data['phone'];
            $customer_email = $customer_data['email'];
            $customer_address = $customer_data['address'];
        } else {
            $validation_errors['customer_id'] = "Selected customer does not exist";
        }
    } else {
        $validation_errors['customer_id'] = "Please select a customer.";
    }
    
    // Validate sale information
    $invoice_number = sanitize_input($_POST['invoice_number']);
    $payment_method = sanitize_input($_POST['payment_method']);
    
    $subtotal_result = sanitize_and_validate($_POST['subtotal'], 'numeric');
    $subtotal = $subtotal_result['value'];
    if (!$subtotal_result['valid']) {
        $validation_errors['subtotal'] = "Subtotal must be a valid number";
    }
    
    $tax_result = sanitize_and_validate($_POST['tax'], 'numeric');
    $tax = $tax_result['value'];
    if (!$tax_result['valid']) {
        $validation_errors['tax'] = "Tax must be a valid number";
    }
    
    $total_result = sanitize_and_validate($_POST['total_amount'], 'numeric');
    $total_amount = $total_result['value'];
    if (!$total_result['valid']) {
        $validation_errors['total_amount'] = "Total amount must be a valid number";
    }
    
    // Validate product items
    if (!isset($_POST['product_id']) || empty($_POST['product_id'])) {
        $validation_errors['products'] = "Please add at least one product";
    } else {
        $product_ids = $_POST['product_id'];
        $quantities = $_POST['quantity'];
        $prices = $_POST['price'];
        $item_totals = $_POST['item_total'];
        
        // Debug: Log product IDs
        error_log("Product IDs: " . print_r($product_ids, true));
        
        // Validate each product item
        for ($i = 0; $i < count($product_ids); $i++) {
            $quantity_result = sanitize_and_validate($quantities[$i], 'numeric');
            if (!$quantity_result['valid']) {
                $validation_errors['quantity_'.$i] = "Quantity must be a valid number";
            }
            
            // Ensure quantity is a whole number
            if ($quantity_result['valid'] && floor($quantity_result['value']) != $quantity_result['value']) {
                $validation_errors['quantity_'.$i] = "Quantity must be a whole number";
            }
            
            // We don't validate price here because we always use the price from the database
            // This ensures prices cannot be manipulated
            
            // We don't validate item_total here because we recalculate it based on the database price
            // and the validated quantity
        }
    }
    
    // If no validation errors, proceed with sale creation
    if (empty($validation_errors)) {
        $tax_rate = 18; // 18% tax rate
        $notes = ''; // Notes are not currently implemented in the form

        // Handle customer creation or update based on selection
        if (isset($_POST['customer_id']) && $_POST['customer_id'] === 'new') {
            // Check if customer with this phone already exists
            $check_customer_query = "SELECT id FROM customers WHERE phone = ?";
            $check_customer_stmt = mysqli_prepare($conn, $check_customer_query);
            mysqli_stmt_bind_param($check_customer_stmt, "s", $customer_phone);
            mysqli_stmt_execute($check_customer_stmt);
            $check_customer_result = mysqli_stmt_get_result($check_customer_stmt);
            
            if (mysqli_num_rows($check_customer_result) > 0) {
                // Customer exists, get ID
                $customer_row = mysqli_fetch_assoc($check_customer_result);
                $customer_id = $customer_row['id'];
                
                // Update contact information
                $update_contact_query = "UPDATE contacts SET first_name = ?, last_name = ?, email = ?, address = ? WHERE id = ?";
                $name_parts = explode(' ', $customer_name, 2);
                $first_name = $name_parts[0];
                $last_name = isset($name_parts[1]) ? $name_parts[1] : '';
                $update_contact_stmt = mysqli_prepare($conn, $update_contact_query);
                mysqli_stmt_bind_param($update_contact_stmt, "ssssi", $first_name, $last_name, $customer_email, $customer_address, $customer_id);
                mysqli_stmt_execute($update_contact_stmt);
            } else {
                // Create new contact as customer
                $name_parts = explode(' ', $customer_name, 2);
                $first_name = $name_parts[0];
                $last_name = isset($name_parts[1]) ? $name_parts[1] : '';
                $insert_contact_query = "INSERT INTO contacts (first_name, last_name, phone, email, address, status, created_by, assigned_to) VALUES (?, ?, ?, ?, ?, 'customer_active', ?, ?)";
                $insert_contact_stmt = mysqli_prepare($conn, $insert_contact_query);
                mysqli_stmt_bind_param($insert_contact_stmt, "sssssii", $first_name, $last_name, $customer_phone, $customer_email, $customer_address, $sales_rep_id, $sales_rep_id);
                mysqli_stmt_execute($insert_contact_stmt);
                $customer_id = mysqli_insert_id($conn);
            }
        }
        // If using existing customer, $customer_id is already set from the form
        
        // Set sale date to current date only (without time)
        $sale_date = date('Y-m-d');

        // Process each product as a separate sale record in the consolidated table
        $all_sales_created = true;
        $created_sale_ids = [];

        for ($i = 0; $i < count($product_ids); $i++) {
            // Make sure product_id is an integer
            $product_id = intval(sanitize_input($product_ids[$i]));

            // Ensure quantity is a whole number
            $quantity = intval(sanitize_input($quantities[$i]));

            // If quantity is less than 1, set it to 1
            if ($quantity < 1) {
                $quantity = 1;
            }

            // Check if product ID exists and get price
            $check_product_query = "SELECT id, price FROM products WHERE id = ?";
            $check_product_stmt = mysqli_prepare($conn, $check_product_query);
            mysqli_stmt_bind_param($check_product_stmt, "i", $product_id);
            mysqli_stmt_execute($check_product_stmt);
            $check_product_result = mysqli_stmt_get_result($check_product_stmt);

            if (mysqli_num_rows($check_product_result) == 0) {
                // Product doesn't exist, handle the error
                $error_message = "Error: Product with ID " . $product_id . " does not exist.";
                $all_sales_created = false;
                break;
            }

            $product_row = mysqli_fetch_assoc($check_product_result);
            $unit_price = $product_row['price'];

            // Calculate amounts for this item
            $item_subtotal = $unit_price * $quantity;
            $item_tax = ($item_subtotal * $tax_rate) / 100;
            $item_total = $item_subtotal + $item_tax;

            // Create individual sale record for each product
            $insert_sale_query = "INSERT INTO sales (
                contact_id, sales_rep_id, sale_date, notes,
                product_id, quantity, unit_price, subtotal, tax, total_amount,
                invoice_number, invoice_status, invoice_date,
                payment_method
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', ?, ?)";

            $insert_sale_stmt = mysqli_prepare($conn, $insert_sale_query);
            mysqli_stmt_bind_param($insert_sale_stmt, "iissidddddsss",
                $customer_id, $sales_rep_id, $sale_date, $notes,
                $product_id, $quantity, $unit_price, $item_subtotal, $item_tax, $item_total,
                $invoice_number, $sale_date, $payment_method
            );

            if (mysqli_stmt_execute($insert_sale_stmt)) {
                $created_sale_ids[] = mysqli_insert_id($conn);
            } else {
                $error_message = "Error creating sale for product ID $product_id: " . mysqli_error($conn);
                $all_sales_created = false;
                break;
            }
        }

        if ($all_sales_created && !empty($created_sale_ids)) {
            // Redirect to invoice page with the first sale ID
            header("Location: invoice.php?id=" . $created_sale_ids[0]);
            exit;
        } else if (!$all_sales_created) {
            // Clean up any created sales if there was an error
            foreach ($created_sale_ids as $sale_id) {
                $delete_sale_query = "DELETE FROM sales WHERE id = ?";
                $delete_sale_stmt = mysqli_prepare($conn, $delete_sale_query);
                mysqli_stmt_bind_param($delete_sale_stmt, "i", $sale_id);
                mysqli_stmt_execute($delete_sale_stmt);
            }
            if (!isset($error_message)) {
                $error_message = "Error: Failed to create sales records.";
            }
        }
    } else {
        $error_message = "Please correct the following errors:";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title>Create Sale - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- <?php include '../includes/head_scripts.php'; ?> -->
    <style>
        .text-color{ color: #FF6500; }
        .input-focus {
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .input-focus:focus {
            border-color: #FF6500;
            box-shadow: 0 0 0 3px rgba(255, 101, 0, 0.1);
        }

        .button-hover {
            transition: background-color 0.3s ease, transform 0.2s ease;
        }

        .button-hover:hover {
            background-color: #e55b00;
            transform: scale(1.05);
        }
    </style>
</head>
<body class="">
<?php include 'includes/navigation.php'; ?>

    <!-- Main Content -->
    <div class="ml-0 min-[870px]:ml-60 min-h-screen flex flex-col">
        <!-- Navbar -->
        <header class="bg-[#1E3E62] p-4 flex justify-between z-10 flex-col">
            <h1 class="text-2xl font-bold text-white mx-10 my-2">Create New Sale</h1>
                    </header>
        <main class="flex-1 overflow-y-auto p-10">
            <!-- Error message -->
            <?php if (isset($error_message)) : ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <strong class="font-bold">Error!</strong>
                    <span class="block sm:inline"><?php echo $error_message; ?></span>
                    <button class="absolute top-0 bottom-0 right-0 px-4 py-3" onclick="this.parentElement.style.display='none'">
                        <svg class="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
                    </button>
                </div>
            <?php endif; ?>

            <form id="sale-form" class="needs-validation" method="POST" action="create_sale.php">
                <input type="hidden" name="action" value="create_sale">
                <input type="hidden" name="invoice_number" value="<?php echo $invoice_number; ?>">
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Customer Details Section -->
                    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                        <div class="px-6 py-5 border-b border-gray-200 sm:px-8" style="background: linear-gradient(to right, #1E3E62, #0B192C);">
                            <h3 class="text-lg leading-6 font-medium text-white">
                                Customer Details
                            </h3>
                            <p class="mt-1 max-w-2xl text-sm text-white">
                                Enter customer information for the invoice
                            </p>
                        </div>
                        <div class="px-4 py-5 sm:p-6 space-y-4">
                            <div>
                                <label for="customer_id" class="block text-sm font-medium text-gray-700">Customer Name *</label>
                                <select name="customer_id" id="customer_id" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border input-focus <?php echo isset($validation_errors['customer_id']) ? 'border-red-500' : ''; ?>" required onchange="populateCustomerDetails(this.value)">
                                    <option value="">Select a customer</option>
                                    <?php while ($customer = mysqli_fetch_assoc($customers_result)): ?>
                                        <option value="<?php echo $customer['id']; ?>" 
                                                data-phone="<?php echo htmlspecialchars($customer['phone']); ?>"
                                                data-email="<?php echo htmlspecialchars($customer['email']); ?>"
                                                data-address="<?php echo htmlspecialchars($customer['address']); ?>">
                                            <?php echo htmlspecialchars($customer['name']); ?>
                                        </option>
                                    <?php endwhile; ?>
                                                                    </select>
                                <?php if (isset($validation_errors['customer_id'])): ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo $validation_errors['customer_id']; ?></p>
                                <?php endif; ?>
                                
                                                            </div>
                            <div>
                                <label for="customer_phone" class="block text-sm font-medium text-gray-700">Phone Number *</label>
                                <input type="tel" name="customer_phone" id="customer_phone" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border input-focus <?php echo isset($validation_errors['customer_phone']) ? 'border-red-500' : ''; ?>" value="<?php echo isset($_POST['customer_phone']) ? htmlspecialchars($_POST['customer_phone']) : ''; ?>" pattern="[5-9][0-9]{4}-?[0-9]{5}" title="Phone number must start with a digit 5-9, followed by 4 digits, optional hyphen, and 5 more digits" required onblur="validatePhoneNumber(this, document.getElementById('phone-validation-result'))">
                                <div id="phone-validation-result" class="mt-1"></div>
                                <?php if (isset($validation_errors['customer_phone'])): ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo $validation_errors['customer_phone']; ?></p>
                                <?php endif; ?>
                            </div>
                            <div>
                                <label for="customer_email" class="block text-sm font-medium text-gray-700">Email Address *</label>
                                <input type="email" name="customer_email" id="customer_email" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border input-focus <?php echo isset($validation_errors['customer_email']) ? 'border-red-500' : ''; ?>" value="<?php echo isset($_POST['customer_email']) ? htmlspecialchars($_POST['customer_email']) : ''; ?>" required>
                                <?php if (isset($validation_errors['customer_email'])): ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo $validation_errors['customer_email']; ?></p>
                                <?php endif; ?>
                            </div>
                            <div>
                                <label for="customer_address" class="block text-sm font-medium text-gray-700">Address *</label>
                                <textarea name="customer_address" id="customer_address" rows="3" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border input-focus <?php echo isset($validation_errors['customer_address']) ? 'border-red-500' : ''; ?>" required><?php echo isset($_POST['customer_address']) ? htmlspecialchars($_POST['customer_address']) : ''; ?></textarea>
                                <?php if (isset($validation_errors['customer_address'])): ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo $validation_errors['customer_address']; ?></p>
                                <?php endif; ?>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Invoice Number</label>
                                <div class="mt-1 p-2 bg-gray-100 rounded-md text-gray-700"><?php echo $invoice_number; ?></div>
                            </div>
                        </div>
                    </div>

                    <!-- Product Search and Selection -->
                    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                        <div class="px-6 py-5 border-b border-gray-200 sm:px-8" style="background: linear-gradient(to right, #1E3E62, #0B192C);">
                            <h3 class="text-lg leading-6 font-medium text-white">
                                Product Selection
                            </h3>
                            <p class="mt-1 max-w-2xl text-sm text-white">
                                Search and add products to the sale (prices are fixed and cannot be changed)
                            </p>
                        </div>
                        <div class="px-4 py-5 sm:p-6 space-y-4">
                            <div>
                                <label for="product_search" class="block text-sm font-medium text-gray-700">Search Products</label>
                                <input type="text" id="product_search" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border input-focus" placeholder="Type to search products...">
                            </div>
                            <div class="product-list max-h-60 overflow-y-auto border rounded-md p-2">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                            <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fixed Price</th>
                                            <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200" id="product-search-results">
                                        <?php 
                                        if (mysqli_num_rows($products_result) > 0) {
                                            while ($row = mysqli_fetch_assoc($products_result)) {
                                                echo '<tr class="product-item" data-id="' . $row['id'] . '" data-name="' . htmlspecialchars($row['name']) . '" data-price="' . $row['price'] . '">';
                                                echo '<td class="px-3 py-2 whitespace-nowrap">';
                                                echo '<div class="text-sm font-medium text-gray-900">' . htmlspecialchars($row['name']) . '</div>';
                                                echo '<div class="text-xs text-gray-500">' . htmlspecialchars($row['category']) . '</div>';
                                                echo '</td>';
                                                echo '<td class="px-3 py-2 whitespace-nowrap">';
                                                echo '<div class="text-sm text-gray-900">' . format_currency($row['price']) . '</div>';
                                                echo '</td>';
                                                echo '<td class="px-3 py-2 whitespace-nowrap">';
                                                echo '<input type="number" min="1" step="1" value="1" class="product-quantity w-12 p-1 border rounded text-xs">';
                                                echo '<button type="button" class="add-to-cart bg-[#0b192c] hover:bg-[#1e3e62] text-white py-1 px-2 rounded-sm text-xs">Add</button>';
                                                echo '</td>';
                                                echo '</tr>';
                                            }
                                        } else {
                                            echo '<tr><td colspan="3" class="px-3 py-2 text-center text-gray-500">No products available.</td></tr>';
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cart / Bill Section -->
                <div class="mt-6 bg-white shadow overflow-hidden sm:rounded-lg">
                    <div class="px-6 py-5 border-b border-gray-200 sm:px-8" style="background: linear-gradient(to right, #1E3E62, #0B192C);">
                        <h3 class="text-lg leading-6 font-medium text-white">
                            Cart Items
                        </h3>
                        <p class="mt-1 max-w-2xl text-sm text-white">
                            Products added to the current sale
                        </p>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fixed Price</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200" id="cart-items">
                                    <tr id="empty-cart-message">
                                        <td colspan="5" class="px-6 py-4 text-center text-gray-500">No items added to cart yet.</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Billing & Payment Section -->
                <div class="mt-6 grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                        <div class="px-6 py-5 border-b border-gray-200 sm:px-8" style="background: linear-gradient(to right, #1E3E62, #0B192C);">
                            <h3 class="text-lg leading-6 font-medium text-white">
                                Payment Details
                            </h3>
                            <p class="mt-1 max-w-2xl text-sm text-white">
                                Select payment method and complete the sale
                            </p>
                        </div>
                        <div class="px-4 py-5 sm:p-6 space-y-4">
                            <div>
                                <label for="payment_method" class="block text-sm font-medium text-gray-700">Payment Method</label>
                                <select name="payment_method" id="payment_method" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm input-focus" required>
                                    <option value="Cash">Cash</option>
                                    <option value="UPI">UPI</option>
                                    <option value="Card">Card</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                            <div class="pt-4">
                                <button type="submit" id="generate-invoice-btn" class="inline-flex justify-center py-2 px-6 border border-transparent shadow-sm text-sm font-medium rounded-sm text-white bg-[#0b192c] hover:bg-[#1e3e62] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0b192c]" disabled>
                                   Create Sale
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                        <div class="px-6 py-5 border-b border-gray-200 sm:px-8" style="background: linear-gradient(to right, #1E3E62, #0B192C);">
                            <h3 class="text-lg leading-6 font-medium text-white">
                                Order Summary
                            </h3>
                        </div>
                        <div class="px-4 py-5 sm:p-6">
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Subtotal:</span>
                                    <span class="font-medium" id="subtotal">₹0.00</span>
                                    <input type="hidden" name="subtotal" id="subtotal-input" value="0">
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Tax (18%):</span>
                                    <span class="font-medium" id="tax">₹0.00</span>
                                    <input type="hidden" name="tax" id="tax-input" value="0">
                                </div>
                                <div class="border-t pt-3 mt-3">
                                    <div class="flex justify-between">
                                        <span class="text-lg font-bold">Total:</span>
                                        <span class="text-lg font-bold text-color" id="total">₹0.00</span>
                                        <input type="hidden" name="total_amount" id="total-input" value="0">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </main>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const productSearch = document.getElementById('product_search');
            const productItems = document.querySelectorAll('.product-item');
            const cartItems = document.getElementById('cart-items');
            const emptyCartMessage = document.getElementById('empty-cart-message');
            const subtotalElement = document.getElementById('subtotal');
            const subtotalInput = document.getElementById('subtotal-input');
            const taxElement = document.getElementById('tax');
            const taxInput = document.getElementById('tax-input');
            const totalElement = document.getElementById('total');
            const totalInput = document.getElementById('total-input');
            const generateInvoiceBtn = document.getElementById('generate-invoice-btn');
            const customerIdSelect = document.getElementById('customer_id');
            const newCustomerNameContainer = document.getElementById('new_customer_name_container');
            
            // Search functionality
            productSearch.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                
                productItems.forEach(item => {
                    const productName = item.getAttribute('data-name').toLowerCase();
                    if (productName.includes(searchTerm)) {
                        item.style.display = '';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
            
            // Add to cart functionality
            document.querySelectorAll('.add-to-cart').forEach(button => {
                button.addEventListener('click', function() {
                    const productRow = this.closest('.product-item');
                    // Make sure productId is an integer
                    const productId = parseInt(productRow.getAttribute('data-id'));
                    const productName = productRow.getAttribute('data-name');
                    const productPrice = parseFloat(productRow.getAttribute('data-price'));
                    const quantity = parseInt(productRow.querySelector('.product-quantity').value);
                    
                    // Check if product already in cart - use toString() to ensure consistent comparison
                    const existingItem = document.querySelector(`.cart-item[data-id="${productId.toString()}"]`);
                    
                    if (existingItem) {
                        // Update quantity
                        const qtyInput = existingItem.querySelector('.cart-quantity');
                        const newQty = parseInt(qtyInput.value) + quantity;
                        qtyInput.value = newQty;
                        
                        // Update total using the original product price (not editable)
                        const originalPrice = parseFloat(existingItem.getAttribute('data-price'));
                        const itemTotal = originalPrice * newQty;
                        existingItem.querySelector('.item-total').textContent = formatCurrency(itemTotal);
                        existingItem.querySelector('.item-total-input').value = itemTotal.toFixed(2);
                    } else {
                        // Add new item to cart
                        const itemTotal = productPrice * quantity;
                        
                        // Hide empty cart message
                        emptyCartMessage.style.display = 'none';
                        
                        // Create new row
                        const newRow = document.createElement('tr');
                        newRow.classList.add('cart-item');
                        newRow.setAttribute('data-id', productId.toString());
                        newRow.setAttribute('data-price', productPrice);
                        
                        // Debug: Log product ID
                        console.log("Adding product ID to cart:", productId);
                        
                        newRow.innerHTML = `
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">${productName}</div>
                                <input type="hidden" name="product_id[]" value="${parseInt(productId)}">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <!-- Display price as text only, not editable -->
                                <div class="text-sm text-gray-900">${formatCurrency(productPrice)}</div>
                                <input type="hidden" name="price[]" value="${productPrice.toFixed(2)}">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="number" min="1" step="1" value="${quantity}" class="cart-quantity w-16 p-1 border rounded text-sm" name="quantity[]" onchange="updateItemTotal(this, true)">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900 item-total">${formatCurrency(itemTotal)}</div>
                                <input type="hidden" name="item_total[]" class="item-total-input" value="${itemTotal.toFixed(2)}">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <button type="button" class="text-red-600 hover:text-red-900" onclick="removeCartItem(this)">Remove</button>
                            </td>
                        `;
                        
                        cartItems.appendChild(newRow);
                    }
                    
                    // Update totals
                    updateOrderSummary();
                });
            });
            
            // Initialize
            updateOrderSummary();
        });
        
        // Update item total when quantity changes
        function updateItemTotal(quantityInput, enforceWholeNumbers = false) {
            const cartItem = quantityInput.closest('.cart-item');
            // Get the original price from the data attribute - this ensures we always use the product's original price
            const price = parseFloat(cartItem.getAttribute('data-price'));
            
            // Ensure quantity is a whole number
            let quantity = parseInt(quantityInput.value);
            
            // If the input has a decimal, round it to the nearest whole number
            if (enforceWholeNumbers && quantityInput.value.includes('.')) {
                quantity = Math.round(parseFloat(quantityInput.value));
                quantityInput.value = quantity; // Update the input field
            }
            
            // Ensure minimum quantity is 1
            if (quantity < 1) {
                quantity = 1;
                quantityInput.value = 1;
            }
            
            const itemTotal = price * quantity;
            
            cartItem.querySelector('.item-total').textContent = formatCurrency(itemTotal);
            cartItem.querySelector('.item-total-input').value = itemTotal.toFixed(2);
            
            updateOrderSummary();
        }
        
        // Remove item from cart
        function removeCartItem(button) {
            const cartItem = button.closest('.cart-item');
            cartItem.remove();
            
            // Show empty cart message if no items
            const cartItems = document.querySelectorAll('.cart-item');
            if (cartItems.length === 0) {
                document.getElementById('empty-cart-message').style.display = '';
            }
            
            updateOrderSummary();
        }
        
        // Update order summary
        function updateOrderSummary() {
            const cartItems = document.querySelectorAll('.cart-item');
            let subtotal = 0;
            
            // Calculate subtotal based on each item's original price and quantity
            cartItems.forEach(item => {
                const price = parseFloat(item.getAttribute('data-price'));
                // Ensure quantity is a whole number
                let quantity = parseInt(item.querySelector('.cart-quantity').value);
                
                // If quantity is less than 1, set it to 1
                if (quantity < 1) {
                    quantity = 1;
                    item.querySelector('.cart-quantity').value = 1;
                }
                
                subtotal += price * quantity;
            });
            
            const taxRate = 0.18; // 18% tax
            const tax = subtotal * taxRate;
            const total = subtotal + tax;
            
            document.getElementById('subtotal').textContent = formatCurrency(subtotal);
            document.getElementById('subtotal-input').value = subtotal.toFixed(2);
            
            document.getElementById('tax').textContent = formatCurrency(tax);
            document.getElementById('tax-input').value = tax.toFixed(2);
            
            document.getElementById('total').textContent = formatCurrency(total);
            document.getElementById('total-input').value = total.toFixed(2);
            
            // Enable/disable generate invoice button
            const generateInvoiceBtn = document.getElementById('generate-invoice-btn');
            generateInvoiceBtn.disabled = subtotal <= 0;
        }
        
        // Format currency
        function formatCurrency(amount) {
            return '₹' + amount.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,');
        }
        
        // Function to populate customer details when a customer is selected
        function populateCustomerDetails(customerId) {
            const customerPhone = document.getElementById('customer_phone');
            const customerEmail = document.getElementById('customer_email');
            const customerAddress = document.getElementById('customer_address');
            
            if (customerId) {
                // Get the selected option
                const selectedOption = document.querySelector(`#customer_id option[value="${customerId}"]`);
                
                if (selectedOption) {
                    // Populate fields with customer data
                    customerPhone.value = selectedOption.getAttribute('data-phone') || '';
                    customerEmail.value = selectedOption.getAttribute('data-email') || '';
                    customerAddress.value = selectedOption.getAttribute('data-address') || '';
                    
                    // Make fields read-only for existing customers
                    customerPhone.readOnly = true;
                    customerEmail.readOnly = true;
                    customerAddress.readOnly = true;
                }
            } else {
                // Clear all fields
                customerPhone.value = '';
                customerEmail.value = '';
                customerAddress.value = '';
                
                // Make fields editable
                customerPhone.readOnly = false;
                customerEmail.readOnly = false;
                customerAddress.readOnly = false;
            }
        }
    </script>
    
    <!-- <?php 
    require_once 'includes/phone_validation.php';
    echo get_phone_validation_js(); 
    ?> -->
</body>
</html>
`