/**
 * Dashboard Notification System
 * 
 * This script handles browser notifications for the dashboard.
 * It shows notifications for new leads and other important events.
 */

// Check if browser supports notifications
function notificationsSupported() {
    return 'Notification' in window;
}

// Request notification permission
function requestNotificationPermission() {
    if (!notificationsSupported()) {
        console.log('<PERSON>rowser does not support notifications');
        return Promise.reject('<PERSON>rowser does not support notifications');
    }
    
    if (Notification.permission === 'granted') {
        return Promise.resolve('granted');
    }
    
    if (Notification.permission !== 'denied') {
        return Notification.requestPermission();
    }
    
    return Promise.reject('Notification permission denied');
}

// Show a notification
function showNotification(title, options = {}) {
    if (!notificationsSupported() || Notification.permission !== 'granted') {
        console.log('Cannot show notification: permission not granted');
        return;
    }
    
    // Default options
    const defaultOptions = {
        icon: '/LEADManagement/img/minilogo.jpg',
        badge: '/LEADManagement/img/minilogo.jpg',
        vibrate: [200, 100, 200],
        requireInteraction: false,
        silent: false
    };
    
    // Merge default options with provided options
    const notificationOptions = { ...defaultOptions, ...options };
    
    // Create and show notification
    const notification = new Notification(title, notificationOptions);
    
    // Handle notification click
    notification.onclick = function() {
        window.focus();
        if (options.url) {
            window.location.href = options.url;
        }
        notification.close();
    };
    
    return notification;
}

// Check for new leads
function checkForNewLeads() {
    fetch('/LEADManagement/check_new_leads.php')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.newLeads > 0) {
                showNotification('New Lead Alert', {
                    body: `You have ${data.newLeads} new lead${data.newLeads > 1 ? 's' : ''}!`,
                    url: '/LEADManagement/leads.php'
                });
                
                // Update UI if needed
                updateLeadCountBadge(data.newLeads);
            }
        })
        .catch(error => console.error('Error checking for new leads:', error));
}

// Update the lead count badge in the UI
function updateLeadCountBadge(count) {
    const badge = document.getElementById('new-leads-badge');
    if (badge) {
        badge.textContent = count;
        badge.classList.remove('hidden');
    }
}

// Initialize notifications
function initNotifications() {
    if (notificationsSupported()) {
        // Request permission when user interacts with the page
        document.addEventListener('click', function() {
            if (Notification.permission !== 'granted' && Notification.permission !== 'denied') {
                requestNotificationPermission()
                    .then(permission => {
                        if (permission === 'granted') {
                            console.log('Notification permission granted');
                        }
                    })
                    .catch(error => console.error('Error requesting notification permission:', error));
            }
        }, { once: true });
        
        // Check for new leads periodically (every 60 seconds)
        if (Notification.permission === 'granted') {
            setInterval(checkForNewLeads, 60000);
            // Initial check
            checkForNewLeads();
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initNotifications);