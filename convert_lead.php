<?php
/**
 * Convert Lead to Customer
 * This page handles the conversion of leads to customers
 */

// Include database configuration
$conn = require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if the user is logged in and has appropriate role
ensure_session_started();
require_any_role(["admin", "manager", "sales_rep"]);

$current_user_id = $_SESSION['user_id'];

// Get lead ID from URL
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: leads.php');
    exit;
}

$lead_id = intval($_GET['id']);

// Get lead details
$lead_sql = "SELECT * FROM leads WHERE id = ?";
$lead_stmt = mysqli_prepare($conn, $lead_sql);
mysqli_stmt_bind_param($lead_stmt, "i", $lead_id);
mysqli_stmt_execute($lead_stmt);
$lead_result = mysqli_stmt_get_result($lead_stmt);

if (!$lead = mysqli_fetch_assoc($lead_result)) {
    header('Location: leads.php');
    exit;
}

// Check if lead is already converted
if ($lead['status'] === 'converted') {
    $error_message = "This lead has already been converted to a customer.";
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($error_message)) {
    // Start transaction
    mysqli_begin_transaction($conn);
    
    try {
        // Get additional data from form
        $address = sanitize_input($_POST['address'] ?? '');
        $aadhar_card = sanitize_input($_POST['aadhar_card'] ?? '');
        $pan_card = sanitize_input($_POST['pan_card'] ?? '');
        $category = sanitize_input($_POST['category'] ?? 'Regular');
        $assigned_to = intval($_POST['assigned_to'] ?? $lead['assigned_to']);
        
        // Insert into contacts table
        $contact_sql = "INSERT INTO contacts (
            first_name, 
            last_name, 
            email, 
            phone, 
            country_code,
            source, 
            address, 
            customer_interest, 
            aadhar_card, 
            pan_card, 
            category, 
            assigned_to,
            created_by,
            created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $contact_stmt = mysqli_prepare($conn, $contact_sql);
        mysqli_stmt_bind_param($contact_stmt, "sssssssssssii",
            $lead['first_name'],
            $lead['last_name'],
            $lead['email'],
            $lead['phone'],
            $lead['country_code'],
            $lead['source'],
            $address,
            $lead['customer_interest'],
            $aadhar_card,
            $pan_card,
            $category,
            $assigned_to,
            $current_user_id
        );
        
        if (!mysqli_stmt_execute($contact_stmt)) {
            throw new Exception("Failed to create contact: " . mysqli_error($conn));
        }
        
        $contact_id = mysqli_insert_id($conn);
        
        // Update lead status to converted
        $update_lead_sql = "UPDATE leads SET status = 'converted', updated_at = NOW() WHERE id = ?";
        $update_stmt = mysqli_prepare($conn, $update_lead_sql);
        mysqli_stmt_bind_param($update_stmt, "i", $lead_id);
        
        if (!mysqli_stmt_execute($update_stmt)) {
            throw new Exception("Failed to update lead status: " . mysqli_error($conn));
        }
        
        // Log the conversion
        $log_sql = "INSERT INTO lead_conversion_log (lead_id, contact_id, converted_by, notes) VALUES (?, ?, ?, ?)";
        $log_stmt = mysqli_prepare($conn, $log_sql);
        $conversion_notes = "Lead converted to customer via dashboard";
        mysqli_stmt_bind_param($log_stmt, "iiis", $lead_id, $contact_id, $current_user_id, $conversion_notes);
        mysqli_stmt_execute($log_stmt);
        
        // Create notification for successful conversion
        $notification_sql = "INSERT INTO notifications (user_id, title, message, type, related_id) VALUES (?, ?, ?, 'lead', ?)";
        $notification_stmt = mysqli_prepare($conn, $notification_sql);
        $title = "Lead Converted Successfully";
        $message = "Lead #{$lead_id} ({$lead['first_name']} {$lead['last_name']}) has been converted to customer #{$contact_id}";
        mysqli_stmt_bind_param($notification_stmt, "issi", $current_user_id, $title, $message, $contact_id);
        mysqli_stmt_execute($notification_stmt);
        
        // Commit transaction
        mysqli_commit($conn);
        
        // Redirect to contacts page with success message
        header('Location: contacts.php?converted=1&contact_id=' . $contact_id);
        exit;
        
    } catch (Exception $e) {
        // Rollback transaction
        mysqli_rollback($conn);
        $error_message = "Conversion failed: " . $e->getMessage();
    }
}

// Get employees for assignment dropdown
$employees_sql = "SELECT id, CONCAT(first_name, ' ', last_name) as name FROM employees WHERE role IN ('sales_rep', 'manager') ORDER BY first_name";
$employees_result = mysqli_query($conn, $employees_sql);
$employees = [];
while ($emp = mysqli_fetch_assoc($employees_result)) {
    $employees[] = $emp;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title>Convert Lead to Customer - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .text-color { color: #FF6500; }
    </style>
</head>
<body class="bg-gray-100">

<?php include 'includes/navigation.php'; ?>

<div class="flex">
    <div class="flex-1 ml-0 min-[870px]:ml-60 p-4">
        <main class="max-w-4xl mx-auto">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center gap-4 mb-4">
                    <a href="leads.php" class="text-gray-600 hover:text-gray-800">
                        <i class="fas fa-arrow-left"></i> Back to Leads
                    </a>
                </div>
                <h1 class="text-3xl font-bold text-gray-900">Convert Lead to Customer</h1>
                <p class="text-gray-600">Convert this lead into a customer in your system</p>
            </div>

            <!-- Error Message -->
            <?php if (isset($error_message)): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <!-- Lead Information Card -->
            <div class="bg-white rounded-lg shadow mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Lead Information</h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Name</label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo htmlspecialchars($lead['first_name'] . ' ' . $lead['last_name']); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Email</label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo htmlspecialchars($lead['email']); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Phone</label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo htmlspecialchars($lead['phone']); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Interest</label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo htmlspecialchars($lead['customer_interest']); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Source</label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo ucfirst($lead['source']); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Created</label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo date('M d, Y H:i', strtotime($lead['created_at'])); ?></p>
                        </div>
                    </div>
                    <?php if ($lead['notes']): ?>
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700">Notes</label>
                        <p class="mt-1 text-sm text-gray-900 bg-gray-50 p-3 rounded"><?php echo nl2br(htmlspecialchars($lead['notes'])); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Conversion Form -->
            <?php if ($lead['status'] !== 'converted'): ?>
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Additional Customer Information</h2>
                    <p class="text-sm text-gray-600">Please provide additional information to complete the conversion</p>
                </div>
                <div class="p-6">
                    <form method="POST" class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="address" class="block text-sm font-medium text-gray-700">Address</label>
                                <textarea name="address" id="address" rows="3" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" placeholder="Enter customer address"></textarea>
                            </div>
                            <div>
                                <label for="category" class="block text-sm font-medium text-gray-700">Category</label>
                                <select name="category" id="category" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                    <option value="Regular">Regular</option>
                                    <option value="Premium">Premium</option>
                                    <option value="VIP">VIP</option>
                                </select>
                            </div>
                            <div>
                                <label for="aadhar_card" class="block text-sm font-medium text-gray-700">Aadhar Card</label>
                                <input type="text" name="aadhar_card" id="aadhar_card" maxlength="12" pattern="[0-9]{12}" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" placeholder="12-digit Aadhar number">
                            </div>
                            <div>
                                <label for="pan_card" class="block text-sm font-medium text-gray-700">PAN Card</label>
                                <input type="text" name="pan_card" id="pan_card" maxlength="10" pattern="[A-Z]{5}[0-9]{4}[A-Z]{1}" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" placeholder="PAN number">
                            </div>
                            <div class="md:col-span-2">
                                <label for="assigned_to" class="block text-sm font-medium text-gray-700">Assign To</label>
                                <select name="assigned_to" id="assigned_to" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">Select Employee</option>
                                    <?php foreach ($employees as $employee): ?>
                                        <option value="<?php echo $employee['id']; ?>" <?php echo ($employee['id'] == $lead['assigned_to']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($employee['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="flex justify-end space-x-4">
                            <a href="leads.php" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">Cancel</a>
                            <button type="submit" class="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                                <i class="fas fa-user-plus mr-2"></i>Convert to Customer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            <?php endif; ?>
        </main>
    </div>
</div>

</body>
</html>
