<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>LMS Home</title>
  <link rel="icon" type="image/png" href="/images/logo.title.png">
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet" />
  <link
    href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Open+Sans:wght@400;500;600&display=swap"
    rel="stylesheet" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" />
  <style>
    html, body {
      overflow-x: hidden;
    }

    body {
      margin: right 0;
      padding: 0;
      box-sizing: border-box;
    }

    @keyframes scroll {
      0% {
        transform: translateX(100%);
      }

      100% {
        transform: translateX(-100%);
      }
    }

    .scrolling-wrapper {
      animation: scroll 10s linear infinite;
    }

    .hero-bg {
      background-image: url('/images/download.png');
      background-size: cover;
      background-position: center;
      height: 100%;
    }

    @keyframes slideInLeft {
      0% {
        opacity: 0;
        transform: translateX(-100px);
      }

      100% {
        opacity: 1;
        transform: translateX(0);
      }
    }

    .animate-slide-in-left {
      animation: slideInLeft 1s ease-out both;
    }

    @keyframes typing {
      from {
        width: 0;
      }

      to {
        width: 100%;
      }
    }

    @keyframes blink {
      50% {
        border-color: transparent;
      }
    }

    .typing-text {
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;

      animation:
        typing 4s steps(80, end),
        blink 0.75s step-end infinite;
    }

    :where([class^="ri-"])::before {
      content: "\f3c2";
    }


    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      font-family: 'Inter', sans-serif;
    }

    .stat-number {
      font-family: 'Inter', sans-serif;
      font-weight: 700;
    }

    .feature-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(74, 119, 168, 0.1);
    }

    .timeline-step::before {
      content: '';
      position: absolute;
      left: 50%;
      top: 0;
      bottom: 0;
      width: 2px;
      background-color: #F7A041;
      transform: translateX(-50%);
      z-index: -1;
    }

    .timeline-step:first-child::before {
      top: 50%;
    }

    .timeline-step:last-child::before {
      bottom: 50%;
    }

    .floating {
      animation: float 6s ease-in-out infinite;
      filter: blur(0);
    }

    @keyframes float {
      0% {
        transform: translateY(0px) rotate(0deg);
        filter: blur(0);
      }

      50% {
        transform: translateY(-20px) rotate(5deg);
        filter: blur(1px);
      }

      100% {
        transform: translateY(0px) rotate(0deg);
        filter: blur(0);
      }
    }

    .floating-slow {
      animation: float-slow 8s ease-in-out infinite;
    }

    @keyframes float-slow {
      0% {
        transform: translateY(0px);
      }

      50% {
        transform: translateY(-10px);
      }

      100% {
        transform: translateY(0px);
      }
    }

    .fade-in-right {
      opacity: 0;
      transform: translateX(80px);
      transition: opacity 0.8s ease-out, transform 0.8s ease-out;
    }

    .fade-in-right.visible {
      opacity: 1;
      transform: translateX(0);
    }

    .fade-in-left {
      opacity: 0;
      transform: translateX(-80px);
      transition: opacity 0.8s ease-out, transform 0.8s ease-out;
    }

    .fade-in-left.visible {
      opacity: 1;
      transform: translateX(0);
    }

    @keyframes scroll {
      0% {
        transform: translateX(0%);
      }

      100% {
        transform: translateX(-50%);
      }
    }

    .scroll-animation {
      animation: scroll 40s linear infinite;
    }

    @keyframes blink {
      0% {
        box-shadow: 0 0 0 0 rgba(11, 25, 44, 0.7);
      }

      70% {
        box-shadow: 0 0 0 15px rgba(11, 25, 44, 0);
      }

      100% {
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
      }
    }

    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 1000;
    }

    .modal.show {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .modal-content {
      background: white;
      border-radius: 1rem;
      width: 100%;
      max-width: 800px;
      margin: 20px;
      animation: modalSlideIn 0.3s ease-out;
    }

    @keyframes modalSlideIn {
      from {
        transform: translateY(-100px);
        opacity: 0;
      }

      to {
        transform: translateY(0);
        opacity: 1;
      }
    }
  </style>
</head>

<body class="overflow-x-hidden  bg-white text-black">

  <header class="fixed w-full top-0 z-50">
    <div class="lg:max-w-full mx-auto px-4 sm:px-4 md:px-6 lg:px-8 py-2 sm:py-3 md:py-4">
      <div
        class=" bg-white backdrop-blur-lg rounded-lg sm:rounded-xl lg:rounded-2xl shadow-lg border border-white/20 mx-1 sm:mx-2 md:mx-3 lg:mx-4">
        <div class=" flex justify-between items-center h-12 sm:h-14 md:h-24 px-2 sm:px-4 md:px-6">

          <div>
            <h1
              class="text-lg sm:text-xl md:text-2xl bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent relative">
              <img src="images/logo.png" alt="LMS Logo" class="lg:w-32  w-20 object-contain">
            </h1>
          </div>

          <!-- Desktop Nav -->
          <nav class="hidden lg:flex justify-between items-center">
            <!-- logo -->

            
            <div class="flex  ">
              <a href="index.html"
                class="flex nav-link px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-all duration-300">
                Home
              </a>
              <a href="About.html"
                class="flex px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-all duration-300">
                About
              </a>
              <a href="products.html"
                class=" flex px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-all duration-300">
                Products
              </a>

              <!-- Features Dropdown -->
              <div class="relative">
                <button id="featuresDropdownBtn"
                  class="px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-all duration-300 flex items-center">
                  Features
                  <i class="ri-arrow-down-s-line ml-1 transition-transform duration-200"></i>
                </button>
                <div id="featuresDropdownMenu" class="absolute top-full left-0 mt-2 w-64 bg-white rounded-xl shadow-2xl border border-gray-100 py-2 z-50 hidden">
                  <a href="leadmanagement.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-brain-line text-blue-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Lead Management</div>
                      <div class="text-xs text-gray-500">Smart lead prioritization</div>
                    </div>
                  </a>
                  <a href="salemanagement.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-settings-3-line text-green-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Sales Management</div>
                      <div class="text-xs text-gray-500">Streamline your process</div>
                    </div>
                  </a>
                  <a href="analytics.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-pie-chart-line text-purple-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Advanced Analytics</div>
                      <div class="text-xs text-gray-500">Detailed insights & reports</div>
                    </div>
                  </a>
                  <a href="taskmanager.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-smartphone-line text-orange-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Task Manager</div>
                      <div class="text-xs text-gray-500">Capture from anywhere</div>
                    </div>
                  </a>
                  <a href="customersupport.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-team-line text-indigo-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Customer Support</div>
                      <div class="text-xs text-gray-500">Work together effectively</div>
                    </div>
                  </a>
                  <a href="integration.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-teal-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-shield-check-line text-teal-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Integration</div>
                      <div class="text-xs text-gray-500">Connect seamlessly with tools</div>
                    </div>
                  </a>
                </div>
              </div>

            </div>
            <!-- login -->
            <div class="flex ">
              <a href="contact.html"
                class=" flex items-center font-bold px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black  transition-all duration-300">
                Contact
              </a>

              <a href="/LEADManagement/auth/login.php"
                class=" flex items-center bg-orange-500 rounded-3xl font-bold px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-white  transition-all duration-300">
                Login
              </a>
            </div>

          </nav>

          <!-- Mobile Menu Button -->
          <div class="lg:hidden z-50">
            <button id="mobileMenuBtn" class="focus:outline-none relative w-6 sm:w-7 md:w-8 h-6 sm:h-7 md:h-8">
              <span class="absolute top-1 left-0 w-6 sm:w-7 md:w-8 h-0.5 bg-black transition-transform duration-300"></span>
              <span class="absolute top-3 left-0 w-6 sm:w-7 md:w-8 h-0.5 bg-black transition-opacity duration-300"></span>
              <span class="absolute top-5 left-0 w-6 sm:w-7 md:w-8 h-0.5 bg-black transition-transform duration-300"></span>
            </button>
          </div>
        </div>

        <!-- Mobile Nav Menu -->
        <div id="mobileMenu" class="lg:hidden px-3 sm:px-4 md:px-6 pb-4 sm:pb-5 md:pb-6 pt-2 hidden">
          <div class="flex flex-col space-y-2 sm:space-y-3 md:space-y-4">
            <a href="index.html"
              class="text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200">
              Home
            </a>
            <a href="About.html"
              class="text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200">
              About
            </a>
            <a href="products.html"
              class="text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200">
              Products
            </a>
            <a href="contact.html"
              class="text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200">
              Contact
            </a>

            <!-- Mobile Features Section -->
            <div>
              <button id="mobileFeaturesBtn"
                class="w-full text-left text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200 flex items-center justify-between">
                <span>Features</span>
                <i class="ri-arrow-down-s-line transition-transform duration-200"></i>
              </button>

              <div id="mobileFeaturesMenu" class="ml-4 mt-2 space-y-2 hidden">
                <a href="leadmanagement.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-brain-line mr-2"></i>Lead Management
                </a>
                <a href="salemanagement.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-settings-3-line mr-2"></i>Email
                  Marketing
                </a>
                <a href="analytics.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-pie-chart-line mr-2"></i>Advanced Analytics
                </a>
                <a href="taskmanager.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-smartphone-line mr-2"></i>Task Manager
                </a>
                <a href="customersupport.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-team-line mr-2"></i>Customer Support
                </a>
                <a href="integration.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-shield-check-line mr-2"></i>Integration
                </a>
              </div>
            </div>

            <a href="/LEADManagement/auth/login.php"
              class="bg-[#FF6500] text-white px-4 sm:px-5 md:px-6 py-1.5 sm:py-2 md:py-2.5 text-sm sm:text-base md:text-lg rounded-full font-bold text-center mt-2 hover:bg-[#e55a00] transition-colors duration-200">
              Login
            </a>
          </div>
        </div>
      </div>
    </div>
  </header>


<!-- Hero Section -->
<section class="flex items-center justify-center px-4 sm:px-6 lg:px-8 xl:px-12 mt-8 bg-gradient-to-br from-orange-50 via-blue-50 to-white min-h-screen-auto pt-20 sm:pt-24 lg:pt-28">
  <div class="w-full mx-auto flex flex-col lg:flex-row items-center justify-between gap-8 lg:gap-12 xl:gap-16">

    <!-- Left Content -->
    <div class="w-full lg:w-1/1 text-center lg:text-left px-4 sm:px-6 lg:px-0" data-aos="fade-right">
      <p class="text-gray-700 font-bold text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl mb-3 sm:mb-4">
        <span class="typing-text">LEAD MANAGEMENT SYSTEM</span>
      </p>

      <h1 class="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-semibold leading-tight mb-4 sm:mb-6">
        Convert <span class="text-[#FF6500] underline font-bold">Conversation</span> into
        <span class="underline text-[#1E3E62] font-bold">Customer</span> with a smart, efficient lead system
      </h1>

      <p class="text-sm sm:text-base md:text-lg lg:text-xl text-gray-800 mb-6 sm:mb-8 leading-relaxed">
        From first click to final close, streamline your entire sales journey.
        Capture leads, automate follow-ups, and turn every opportunity into revenue — all from one clean, powerful dashboard.
      </p>

      <!-- CTA Button -->
      <div class="flex justify-center lg:justify-start">
        <a href="products.html">
          <button
            class="bg-[#1E3E62] text-white px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-semibold hover:scale-105 transition-transform duration-300 flex items-center gap-2 text-sm sm:text-base md:text-lg">
            See Our Works
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5" fill="none" stroke="currentColor"
              stroke-width="2" viewBox="0 0 24 24">
              <path d="M5 12h14M12 5l7 7-7 7" />
            </svg>
          </button>
        </a>
      </div>
    </div>

    <!-- Right Image Section -->
    <div class="w-full lg:w-1/2 flex justify-center lg:justify-end px-4 sm:px-6 lg:px-0" data-aos="fade-left">
      <img 
        src="images/herobg.png" 
        alt="Lead Management Illustration"
        loading="lazy"
        class="w-full sm:max-w-sm md:max-w-md lg:max-w-lg xl:max-w-xl 2xl:max-w-2xl object-contain"
      >
    </div>
  </div>
</section>


<!-- Brand Section -->
<section class="py-12 sm:py-16 lg:py-20 px-4 sm:px-6 lg:px-8 bg-gray-50 sm:-mt-12 lg:-mt-4">
  <div class="w-full mx-auto">
    <h2 class="text-xl sm:text-2xl lg:text-3xl font-bold sm:mb-10 lg:mb-4 text-center">Our Recent Clients & Partners</h2>
    <div class="overflow-hidden relative w-full">
      <div class="w-auto flex scrolling-wrapper space-x-8 sm:space-x-12 lg:space-x-16 items-center">

        <!-- Amazon -->
        <div class="flex items-center gap-2 min-w-[120px] sm:min-w-[140px] lg:min-w-[160px]">
          <img src="https://upload.wikimedia.org/wikipedia/commons/a/a9/Amazon_logo.svg" alt="Amazon"
            class="h-6 w-auto sm:h-8 lg:h-10" />
          <span class="font-medium text-sm sm:text-base lg:text-lg">Amazon</span>
        </div>
        <!-- Flipkart -->
        <div class="flex items-center gap-2 min-w-[120px] sm:min-w-[140px] lg:min-w-[160px]">
          <img src="images/flipkart.png" alt="Flipkart" class="h-6 w-auto sm:h-8 lg:h-10" />
          <span class="font-medium text-sm sm:text-base lg:text-lg">Flipkart</span>
        </div>
        <!-- Myntra -->
        <div class="flex items-center gap-2 min-w-[120px] sm:min-w-[140px] lg:min-w-[160px]">
          <img src="images/myantra.png" alt="Myntra" class="h-7 w-auto sm:h-9 lg:h-11" />
          <span class="font-medium text-sm sm:text-base lg:text-lg">Myntra</span>
        </div>
        <!-- Meesho -->
        <div class="flex items-center gap-2 min-w-[120px] sm:min-w-[140px] lg:min-w-[160px]">
          <img src="images/meesho.png" alt="Meesho" class="h-5 w-auto sm:h-6 lg:h-8" />
          <span class="font-medium text-sm sm:text-base lg:text-lg">Meesho</span>
        </div>
        <!-- Swiggy -->
        <div class="flex items-center gap-2 min-w-[120px] sm:min-w-[140px] lg:min-w-[160px]">
          <img src="images/swiggy.png" alt="Swiggy" class="h-6 w-auto sm:h-8 lg:h-10" />
          <span class="font-medium text-sm sm:text-base lg:text-lg">Swiggy</span>
        </div>

        <!-- Repeat to create continuous scroll -->
        <div class="flex items-center gap-2 min-w-[120px] sm:min-w-[140px] lg:min-w-[160px]">
          <img src="https://upload.wikimedia.org/wikipedia/commons/a/a9/Amazon_logo.svg" alt="Amazon"
            class="h-6 w-auto sm:h-8 lg:h-10" />
          <span class="font-medium text-sm sm:text-base lg:text-lg">Amazon</span>
        </div>
        <div class="flex items-center gap-2 min-w-[120px] sm:min-w-[140px] lg:min-w-[160px]">
          <img src="images/flipkart.png" alt="Flipkart" class="h-6 w-auto sm:h-8 lg:h-10" />
          <span class="font-medium text-sm sm:text-base lg:text-lg">Flipkart</span>
        </div>
        <div class="flex items-center gap-2 min-w-[120px] sm:min-w-[140px] lg:min-w-[160px]">
          <img src="images/myantra.png" alt="Myntra" class="h-7 w-auto sm:h-9 lg:h-11" />
          <span class="font-medium text-sm sm:text-base lg:text-lg">Myntra</span>
        </div>
        <div class="flex items-center gap-2 min-w-[120px] sm:min-w-[140px] lg:min-w-[160px]">
          <img src="images/meesho.png" alt="Meesho" class="h-5 w-auto sm:h-6 lg:h-8" />
          <span class="font-medium text-sm sm:text-base lg:text-lg">Meesho</span>
        </div>
        <div class="flex items-center gap-2 min-w-[120px] sm:min-w-[140px] lg:min-w-[160px]">
          <img src="images/swiggy.png" alt="Swiggy" class="h-6 w-auto sm:h-8 lg:h-10" />
          <span class="font-medium text-sm sm:text-base lg:text-lg">Swiggy</span>
        </div>

      </div>
    </div>
  </div>
</section>

<section id="features" class="pt-8 sm:pt-12 lg:pt-16 pb-16 sm:pb-20 ml-6 mr-6 lg:pb-24 bg-white">

  <div class="w-full mx-auto px-4 sm:px-6 lg:px-8" data-aos="fade-up">
    
    <!-- Section Title -->
    <div class="text-center mb-12 sm:mb-16" data-aos="fade-up">
      <h2 class="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 text-transparent bg-gradient-to-r from-orange-500 to-blue-500 bg-clip-text mb-4 sm:mb-6">
        Features That Redefine Success with a Smarter Lead System
      </h2>
      <p class="text-sm sm:text-base md:text-lg text-gray-600 w-full mx-auto px-4">
        <span class="font-bold text-black">Everything you need — nothing you don't.</span><br>
        Smart automation, real-time visibility, and sales tools that actually help you close.
      </p>
    </div>

    <!-- Feature Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 lg:gap-10">
      
      <!-- Lead Tracking -->
      <div class="group relative text-center overflow-hidden bg-white p-4 sm:p-6 h-[240px] sm:h-[260px] flex flex-col justify-start rounded-xl shadow-md ring-1 ring-gray-200 hover:-translate-y-1 hover:shadow-2xl transition-all duration-300" data-aos="fade-up">
        <span class="absolute top-6 left-1/2 -translate-x-1/2 z-0 h-14 w-14 rounded-full bg-[#0b192c] group-hover:scale-[8] transition-all duration-300"></span>
        <div class="relative z-10">
          <div class="w-10 h-10 sm:w-12 sm:h-12 mx-auto mb-3 sm:mb-4 grid place-items-center rounded-full bg-[#0b192c] group-hover:bg-gray-700 transition">
            <i class="ri-user-follow-line text-white text-lg sm:text-xl"></i>
          </div>
          <h3 class="text-base sm:text-lg lg:text-xl font-bold text-gray-900 mb-2 group-hover:text-white transition">Lead Tracking</h3>
          <p class="text-xs sm:text-sm lg:text-base text-gray-600 group-hover:text-white/90 transition">
            Capture and organize leads from multiple sources with our intuitive tracking system.
          </p>
        </div>
      </div>

      <!-- Sales Analytics -->
      <div class="group relative text-center overflow-hidden bg-white p-4 sm:p-6 h-[240px] sm:h-[260px] flex flex-col justify-start rounded-xl shadow-md ring-1 ring-gray-200 hover:-translate-y-1 hover:shadow-2xl transition-all duration-300" data-aos="fade-up" data-aos-delay="100">
        <span class="absolute top-6 left-1/2 -translate-x-1/2 z-0 h-14 w-14 rounded-full bg-[#FF6500] group-hover:scale-[8] transition-all duration-300"></span>
        <div class="relative z-10">
          <div class="w-10 h-10 sm:w-12 sm:h-12 mx-auto mb-3 sm:mb-4 grid place-items-center rounded-full bg-[#FF6500] group-hover:bg-[#ff7a26] transition">
            <i class="ri-line-chart-line text-white text-lg sm:text-xl"></i>
          </div>
          <h3 class="text-base sm:text-lg lg:text-xl font-bold text-gray-900 mb-2 group-hover:text-white transition">Sales Analytics</h3>
          <p class="text-xs sm:text-sm lg:text-base text-gray-600 group-hover:text-white/90 transition">
            Gain valuable insights with comprehensive reports and real-time analytics dashboard.
          </p>
        </div>
      </div>

      <!-- Customer Management -->
      <div class="group relative text-center overflow-hidden bg-white p-4 sm:p-6 h-[240px] sm:h-[260px] flex flex-col justify-start rounded-xl shadow-md ring-1 ring-gray-200 hover:-translate-y-1 hover:shadow-2xl transition-all duration-300" data-aos="fade-up" data-aos-delay="200">
        <span class="absolute top-6 left-1/2 -translate-x-1/2 z-0 h-14 w-14 rounded-full bg-[#1E3E62] group-hover:scale-[8] transition-all duration-300"></span>
        <div class="relative z-10">
          <div class="w-10 h-10 sm:w-12 sm:h-12 mx-auto mb-3 sm:mb-4 grid place-items-center rounded-full bg-[#1E3E62] group-hover:bg-[#35547a] transition">
            <i class="ri-group-line text-white text-lg sm:text-xl"></i>
          </div>
          <h3 class="text-base sm:text-lg lg:text-xl font-semibold text-gray-900 mb-2 group-hover:text-white transition">Customer Management</h3>
          <p class="text-xs sm:text-sm lg:text-base text-gray-600 group-hover:text-white/90 transition">
            Maintain detailed customer profiles and interaction history in one central location.
          </p>
        </div>
      </div>

      <!-- Product Performance -->
      <div class="group relative text-center overflow-hidden bg-white p-4 sm:p-6 h-[240px] sm:h-[260px] flex flex-col justify-start rounded-xl shadow-md ring-1 ring-gray-200 hover:-translate-y-1 hover:shadow-2xl transition-all duration-300" data-aos="fade-up" data-aos-delay="300">
        <span class="absolute top-6 left-1/2 -translate-x-1/2 z-0 h-14 w-14 rounded-full bg-[#0B192C] group-hover:scale-[8] transition-all duration-300"></span>
        <div class="relative z-10">
          <div class="w-10 h-10 sm:w-12 sm:h-12 mx-auto mb-3 sm:mb-4 grid place-items-center rounded-full bg-[#0B192C] group-hover:bg-[#1a2b42] transition">
            <i class="ri-shopping-bag-line text-white text-lg sm:text-xl"></i>
          </div>
          <h3 class="text-base sm:text-lg lg:text-xl font-semibold text-gray-900 mb-2 group-hover:text-white transition">Product Performance</h3>
          <p class="text-xs sm:text-sm lg:text-base text-gray-600 group-hover:text-white/90 transition">
            Track which products are selling best and optimize your inventory accordingly.
          </p>
        </div>
      </div>

    </div>
  </div>
</section>

<!-- Use Cases / Industries Served Section -->
<section class="pt-8 sm:pt-12 lg:pt-16 pb-16 sm:pb-20 mr-6 lg:pb-24 bg-white">
  <div class="w-full mx-auto px-4 ml-4 mr-4 sm:px-6 lg:px-8" data-aos="fade-up">

    <!-- Section Title -->
    <div class="text-center mb-12 sm:mb-16">
      <h2 class="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 text-transparent bg-gradient-to-r from-orange-500 to-blue-500 bg-clip-text mb-4 sm:mb-6">
        Who Uses Our LMS?
      </h2>
      <p class="text-sm sm:text-base md:text-lg text-gray-600 w-full mx-auto px-4">
        Built for every industry that values leads and customer relationships.
      </p>
    </div>

    <!-- Industry Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 lg:gap-10" data-aos="fade-up">

      <!-- E-Commerce -->
      <div class="group relative text-center overflow-hidden bg-white p-6 sm:p-8 h-[280px] sm:h-[300px] flex flex-col justify-start rounded-xl shadow-md ring-1 ring-gray-200 hover:-translate-y-1 hover:shadow-2xl transition-all duration-300" data-aos="fade-up">
        <span class="absolute top-6 left-1/2 -translate-x-1/2 z-0 h-16 w-16 rounded-full bg-[#FF6500] group-hover:scale-[8] transition-all duration-300"></span>
        <div class="relative z-10">
          <div class="w-12 h-12 sm:w-14 sm:h-14 mx-auto mb-4 grid place-items-center rounded-full bg-[#FF6500] group-hover:bg-[#ff7a26] transition" data-aos="fade-up">
            <i class="ri-store-2-line text-white text-xl sm:text-2xl"></i>
          </div>
          <h3 class="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 mb-2 group-hover:text-white transition">E-Commerce</h3>
          <p class="text-sm sm:text-base lg:text-lg text-gray-600 group-hover:text-white/90 transition">
            Convert traffic into sales using real-time engagement tools and behavior-based triggers.
          </p>
        </div>
      </div>

      <!-- Real Estate -->
      <div class="group relative text-center overflow-hidden bg-white p-6 sm:p-8 h-[280px] sm:h-[300px] flex flex-col justify-start rounded-xl shadow-md ring-1 ring-gray-200 hover:-translate-y-1 hover:shadow-2xl transition-all duration-300" data-aos="fade-up">
        <span class="absolute top-6 left-1/2 -translate-x-1/2 z-0 h-16 w-16 rounded-full bg-[#1E3E62] group-hover:scale-[8] transition-all duration-300"></span>
        <div class="relative z-10">
          <div class="w-12 h-12 sm:w-14 sm:h-14 mx-auto mb-4 grid place-items-center rounded-full bg-[#1E3E62] group-hover:bg-[#35547a] transition">
            <i class="ri-building-line text-white text-xl sm:text-2xl"></i>
          </div>
          <h3 class="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 mb-2 group-hover:text-white transition">Real Estate</h3>
          <p class="text-sm sm:text-base lg:text-lg text-gray-600 group-hover:text-white/90 transition">
            Stay connected with prospects, streamline property promotions, and increase conversions.
          </p>
        </div>
      </div>

      <!-- SaaS Companies -->
      <div class="group relative text-center overflow-hidden bg-white p-6 sm:p-8 h-[280px] sm:h-[300px] flex flex-col justify-start rounded-xl shadow-md ring-1 ring-gray-200 hover:-translate-y-1 hover:shadow-2xl transition-all duration-300" data-aos="fade-up">
        <span class="absolute top-6 left-1/2 -translate-x-1/2 z-0 h-16 w-16 rounded-full bg-[#0B192C] group-hover:scale-[8] transition-all duration-300"></span>
        <div class="relative z-10">
          <div class="w-12 h-12 sm:w-14 sm:h-14 mx-auto mb-4 grid place-items-center rounded-full bg-[#0B192C] group-hover:bg-[#1a2b42] transition">
            <i class="ri-briefcase-4-line text-white text-xl sm:text-2xl"></i>
          </div>
          <h3 class="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 mb-2 group-hover:text-white transition">SaaS Companies</h3>
          <p class="text-sm sm:text-base lg:text-lg text-gray-600 group-hover:text-white/90 transition">
            Automate your trial-to-paid funnel and streamline customer onboarding with precision.
          </p>
        </div>
      </div>

     <!-- Healthcare (Updated Card Color) -->
<div class="group relative text-center overflow-hidden bg-white p-6 sm:p-8 h-[280px] sm:h-[300px] flex flex-col justify-start rounded-xl shadow-md ring-1 ring-gray-200 hover:-translate-y-1 hover:shadow-2xl transition-all duration-300" data-aos="fade-up">
  <span class="absolute top-6 left-1/2 -translate-x-1/2 z-0 h-16 w-16 rounded-full bg-blue-400 group-hover:scale-[8] transition-all duration-300"></span>
  <div class="relative z-10">
    <div class="w-12 h-12 sm:w-14 sm:h-14 mx-auto mb-4 grid place-items-center rounded-full bg-blue-500 group-hover:bg-blue-600 transition">
      <i class="ri-hospital-line text-white text-xl sm:text-2xl"></i>
    </div>
    <h3 class="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 mb-2 group-hover:text-white transition">Healthcare</h3>
    <p class="text-sm sm:text-base lg:text-lg text-gray-600 group-hover:text-white/90 transition">
      Enhance patient communication, reduce no-shows, and deliver seamless experiences.
    </p>
  </div>
</div>


    </div>
  </div>
</section>



 <!-- How It Works Section -->
<section class="pt-4 sm:pt-6 lg:pt-8 pb-16 sm:pb-20 lg:pb-24 bg-white">
  <div class="w-auto mx-auto px-4 sm:px-6 lg:px-8 ml-4 mr-4" data-aos="fade-up">

    <!-- Section Title -->
    <div class="text-center mb-12" data-aos="fade-up">
      <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 text-transparent bg-gradient-to-r from-orange-500 to-blue-500 bg-clip-text mb-4">
        How It Works
      </h2>
      <p class="text-gray-600 text-lg max-w-3xl mx-auto">A simple, powerful workflow from lead capture to conversion.</p>
    </div>

    <!-- Steps Grid -->
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 lg:gap-8">

      <!-- Repeat for each card -->
      <div class="group relative text-center overflow-hidden bg-white p-6 h-[300px] flex flex-col justify-start rounded-xl shadow-md ring-1 ring-gray-200 transition-all duration-300 cursor-pointer hover:-translate-y-1 hover:shadow-2xl" data-aos="fade-up" data-aos-delay="0">
        
        <!-- Animated Top Border -->
        <div class="absolute top-0 left-0 h-1 w-0 group-hover:w-full transition-all duration-500 bg-gradient-to-r from-orange-500 via-pink-500 to-blue-500 rounded-t-xl"></div>

        <div class="w-14 h-14 mx-auto mb-5 grid place-items-center rounded-full bg-[#FF6500] text-white text-2xl font-bold">
          1
        </div>
        <h3 class="text-2xl font-bold text-gray-900 mb-2 group-hover:text-[#FF6500] transition">Capture Leads</h3>
        <p class="text-gray-600 text-xl group-hover:text-gray-900 transition">
          Collect leads from websites, forms, ads, and third-party integrations.
        </p>
      </div>

      <!-- Step 2 -->
      <div class="group relative text-center overflow-hidden bg-white p-6 h-[300px] flex flex-col justify-start rounded-xl shadow-md ring-1 ring-gray-200 transition-all duration-300 cursor-pointer hover:-translate-y-1 hover:shadow-2xl" data-aos="fade-up" data-aos-delay="100">
        <div class="absolute top-0 left-0 h-1 w-0 group-hover:w-full transition-all duration-500 bg-gradient-to-r from-orange-500 via-pink-500 to-blue-500 rounded-t-xl"></div>

        <div class="w-14 h-14 mx-auto mb-5 grid place-items-center rounded-full bg-[#1E3E62] text-white text-2xl font-bold">
          2
        </div>
        <h3 class="text-2xl font-bold text-gray-900 mb-2 group-hover:text-[#1E3E62] transition">Nurture & Engage</h3>
        <p class="text-gray-600 text-xl group-hover:text-gray-900 transition">
          Automate follow-ups through email, SMS, or WhatsApp workflows.
        </p>
      </div>

      <!-- Step 3 -->
      <div class="group relative text-center overflow-hidden bg-white p-6 h-[300px] flex flex-col justify-start rounded-xl shadow-md ring-1 ring-gray-200 transition-all duration-300 cursor-pointer hover:-translate-y-1 hover:shadow-2xl" data-aos="fade-up" data-aos-delay="200">
        <div class="absolute top-0 left-0 h-1 w-0 group-hover:w-full transition-all duration-500 bg-gradient-to-r from-orange-500 via-pink-500 to-blue-500 rounded-t-xl"></div>

        <div class="w-14 h-14 mx-auto mb-5 grid place-items-center rounded-full bg-[#0B192C] text-white text-2xl font-bold">
          3
        </div>
        <h3 class="text-2xl font-bold text-gray-900 mb-2 group-hover:text-[#0B192C] transition">Track Progress</h3>
        <p class="text-gray-600 text-xl group-hover:text-gray-900 transition">
          View each lead's journey with visual pipelines and live dashboards.
        </p>
      </div>

      <!-- Step 4 -->
      <div class="group relative text-center overflow-hidden bg-white p-6 h-[300px] flex flex-col justify-start rounded-xl shadow-md ring-1 ring-gray-200 transition-all duration-300 cursor-pointer hover:-translate-y-1 hover:shadow-2xl" data-aos="fade-up" data-aos-delay="300">
        <div class="absolute top-0 left-0 h-1 w-0 group-hover:w-full transition-all duration-500 bg-gradient-to-r from-orange-500 via-pink-500 to-blue-500 rounded-t-xl"></div>

        <div class="w-14 h-14 mx-auto mb-5 grid place-items-center rounded-full bg-[#F7A041] text-white text-2xl font-bold">
          4
        </div>
        <h3 class="text-2xl font-bold text-gray-900 mb-2 group-hover:text-[#F7A041] transition">Close Deals</h3>
        <p class="text-gray-600 text-xl group-hover:text-gray-900 transition">
          Move hot leads to your sales team and close faster with confidence.
        </p>
      </div>

    </div>
  </div>
</section>




  <!-- Team Collaboration Overview Section -->
  <section class="pt-4 sm:pt-6 lg:pt-8 pb-16 sm:pb-20 lg:pb-24 bg-white">
    <div class="w-auto mx-auto ml-12 mr-12" data-aos="fade-up">
      <div class="text-left mb-16 " data-aos="fade-up">
        <h2 class="text-4xl sm:text-5xl font-bold text-center text-transparent bg-gradient-to-r from-orange-500 to-blue-500 bg-clip-text mb-4" data-aos="fade-up">
          Products
        </h2>
      </div>

      <div class="grid md:grid-cols-2 gap-12 items-center mb-6" data-aos="fade-up">
        <!-- Left Image / Card -->
        <div class="bg-[#FF6500] p-8 rounded-xl shadow-md flex justify-center items-center" data-aos="fade-up">
          <div class="bg-white p-6 rounded-lg shadow w-full max-w-md">
            <h4 class="text-sm font-semibold text-gray-700 mb-4">LEAD MANAGEMENT</h4>
            <div class="space-y-2">
              <div class="flex items-center gap-2">
                <span class="w-3 h-3 bg-blue-800 rounded-full"></span>
                <div class="h-1 w-2/3 bg-gray-300 rounded"></div>
              </div>
              <div class="flex items-center gap-2">
                <span class="w-3 h-3 bg-pink-700 rounded-full"></span>
                <div class="h-1 w-1/2 bg-gray-300 rounded"></div>
              </div>
              <div class="flex items-center gap-2">
                <span class="w-3 h-3 bg-purple-700 rounded-full"></span>
                <div class="h-1 w-4/5 bg-gray-300 rounded"></div>
              </div>
              <div class="flex items-center gap-2">
                <span class="w-3 h-3 bg-cyan-500 rounded-full"></span>
                <div class="h-1 w-1/3 bg-gray-300 rounded"></div>
              </div>
            </div>
            <!-- Bar Graph Style -->
            <div class="mt-6 flex items-end justify-between gap-2">
              <div class="w-3 h-12 bg-purple-800 rounded"></div>
              <div class="w-3 h-8 bg-blue-400 rounded"></div>
              <div class="w-3 h-10 bg-cyan-400 rounded"></div>
              <div class="w-3 h-16 bg-indigo-800 rounded"></div>
              <div class="w-3 h-14 bg-pink-300 rounded"></div>
            </div>
          </div>
        </div>

        <!-- Right Text Content -->
        <div>
          <h3 class="text-2xl md:text-3xl font-bold mb-4" data-aos="fade-up">Lead Management </h3>
          <p class="text-gray-700 mb-6" data-aos="fade-up">
            Emphasize how your sales SaaS features enable seamless collaboration among team members, ensuring everyone
            stays on the same page, shares vital information, and works together towards achieving deals and targets.
          </p>
          <a href="leadmanagement.html"><button
              class="bg-[#1E3E62] text-white px-5 py-2 rounded-full font-medium flex items-center gap-1 hover:scale-105 transition-transform"
              data-aos="fade-up">
              Learn More</a>
          <span class="text-xl">➔</span>
          </button>
        </div>
      </div>


      <div class="grid md:grid-cols-2 gap-12 items-center" data-aos="fade-up">
        <!-- Left Image / Card -->
        <div class="bg-[#1E3E62] p-8 rounded-xl shadow-md flex justify-center items-center mb-6" data-aos="fade-up">
          <div class="bg-white p-6 rounded-lg shadow w-full max-w-md" data-aos="fade-up">
            <h4 class="text-sm font-semibold text-gray-700 mb-4">SALES MANAGEMENT</h4>
            <div class="space-y-2">
              <div class="flex items-center gap-2">
                <span class="w-3 h-3 bg-blue-800 rounded-full"></span>
                <div class="h-1 w-2/3 bg-gray-300 rounded"></div>
              </div>
              <div class="flex items-center gap-2">
                <span class="w-3 h-3 bg-pink-700 rounded-full"></span>
                <div class="h-1 w-1/2 bg-gray-300 rounded"></div>
              </div>
              <div class="flex items-center gap-2">
                <span class="w-3 h-3 bg-purple-700 rounded-full"></span>
                <div class="h-1 w-4/5 bg-gray-300 rounded"></div>
              </div>
              <div class="flex items-center gap-2">
                <span class="w-3 h-3 bg-cyan-500 rounded-full"></span>
                <div class="h-1 w-1/3 bg-gray-300 rounded"></div>
              </div>
            </div>
            <!-- Bar Graph Style -->
            <div class="mt-6 flex items-end justify-between gap-2">
              <div class="w-3 h-12 bg-purple-800 rounded"></div>
              <div class="w-3 h-8 bg-blue-400 rounded"></div>
              <div class="w-3 h-10 bg-cyan-400 rounded"></div>
              <div class="w-3 h-16 bg-indigo-800 rounded"></div>
              <div class="w-3 h-14 bg-pink-300 rounded"></div>
            </div>
          </div>
        </div>

        <!-- Right Text Content -->
        <div>
          <h3 class="text-2xl md:text-3xl font-bold mb-4" data-aos="fade-up">Sales Management </h3>
          <p class="text-gray-700 mb-6" data-aos="fade-up">
            Emphasize how your sales SaaS features enable seamless collaboration among team members, ensuring everyone
            stays on the same page, shares vital information, and works together towards achieving deals and targets.
          </p>
          <a href="salesmanagement.html"><button
              class="bg-[#1E3E62] text-white px-5 py-2 rounded-full font-medium flex items-center gap-1 hover:scale-105 transition-transform"
              data-aos="fade-up">
              Learn More</a>
          <span class="text-xl">➔</span>
          </button>
        </div>
      </div>
      <div class="grid md:grid-cols-2 gap-12 items-center" data-aos="fade-up">
        <!-- Left Image / Card -->
        <div class="bg-[#0B192C] p-8 rounded-xl shadow-md flex justify-center items-center mt-5" data-aos="fade-up">
          <div class="bg-white p-6 rounded-lg shadow w-full max-w-md" data-aos="fade-up">
            <h4 class="text-sm font-semibold text-gray-700 mb-4">CUSTOMER MANAGEMENT</h4>
            <div class="space-y-2">
              <div class="flex items-center gap-2">
                <span class="w-3 h-3 bg-blue-800 rounded-full"></span>
                <div class="h-1 w-2/3 bg-gray-300 rounded"></div>
              </div>
              <div class="flex items-center gap-2">
                <span class="w-3 h-3 bg-pink-700 rounded-full"></span>
                <div class="h-1 w-1/2 bg-gray-300 rounded"></div>
              </div>
              <div class="flex items-center gap-2">
                <span class="w-3 h-3 bg-purple-700 rounded-full"></span>
                <div class="h-1 w-4/5 bg-gray-300 rounded"></div>
              </div>
              <div class="flex items-center gap-2">
                <span class="w-3 h-3 bg-cyan-500 rounded-full"></span>
                <div class="h-1 w-1/3 bg-gray-300 rounded"></div>
              </div>
            </div>
            <!-- Bar Graph Style -->
            <div class="mt-6 flex items-end justify-between gap-2">
              <div class="w-3 h-12 bg-purple-800 rounded"></div>
              <div class="w-3 h-8 bg-blue-400 rounded"></div>
              <div class="w-3 h-10 bg-cyan-400 rounded"></div>
              <div class="w-3 h-16 bg-indigo-800 rounded"></div>
              <div class="w-3 h-14 bg-pink-300 rounded"></div>
            </div>
          </div>
        </div>

        <!-- Right Text Content -->
        <div>
          <h3 class="text-2xl md:text-3xl font-bold mb-4" data-aos="fade-up">Customer Management</h3>
          <p class="text-gray-700 mb-6" data-aos="fade-up">
            Emphasize how your sales SaaS features enable seamless collaboration among team members, ensuring everyone
            stays on the same page, shares vital information, and works together towards achieving deals and targets.
          </p>
          <a href="customersupport.html"><button
              class="bg-[#1E3E62] text-white px-5 py-2 rounded-full font-medium flex items-center gap-1 hover:scale-105 transition-transform"
              data-aos="fade-up">
              Learn More</a>
          <span class="text-xl">➔</span>
          </button>
        </div>
      </div>

      <div class="grid md:grid-cols-2 gap-12 items-center mb-6 mt-10" data-aos="fade-up">
        <!-- Left Image / Card -->
        <div class="bg-[#FF6500] p-8 rounded-xl shadow-md flex justify-center items-center" data-aos="fade-up">
          <div class="bg-white p-6 rounded-lg shadow w-full max-w-md">
            <h4 class="text-sm font-semibold text-gray-700 mb-4">Advanced Analytics</h4>
            <div class="space-y-2">
              <div class="flex items-center gap-2">
                <span class="w-3 h-3 bg-blue-800 rounded-full"></span>
                <div class="h-1 w-2/3 bg-gray-300 rounded"></div>
              </div>
              <div class="flex items-center gap-2">
                <span class="w-3 h-3 bg-pink-700 rounded-full"></span>
                <div class="h-1 w-1/2 bg-gray-300 rounded"></div>
              </div>
              <div class="flex items-center gap-2">
                <span class="w-3 h-3 bg-purple-700 rounded-full"></span>
                <div class="h-1 w-4/5 bg-gray-300 rounded"></div>
              </div>
              <div class="flex items-center gap-2">
                <span class="w-3 h-3 bg-cyan-500 rounded-full"></span>
                <div class="h-1 w-1/3 bg-gray-300 rounded"></div>
              </div>
            </div>
            <!-- Bar Graph Style -->
            <div class="mt-6 flex items-end justify-between gap-2">
              <div class="w-3 h-12 bg-purple-800 rounded"></div>
              <div class="w-3 h-8 bg-blue-400 rounded"></div>
              <div class="w-3 h-10 bg-cyan-400 rounded"></div>
              <div class="w-3 h-16 bg-indigo-800 rounded"></div>
              <div class="w-3 h-14 bg-pink-300 rounded"></div>
            </div>
          </div>
        </div>

        <!-- Right Text Content -->
        <div>
          <h3 class="text-2xl md:text-3xl font-bold mb-4" data-aos="fade-up">Advanced Analytics</h3>
          <p class="text-gray-700 mb-6" data-aos="fade-up">
            Real-time insights for performance and quality assurance.
            Monitor KPIs such as CSAT, first response time (FRT), resolution time, and agent utilization. Leverage
            heatmaps, trend visualizations, and sentiment analysis to continuously optimize support workflows

          </p>
          <a href="analytics.html"><button
              class="bg-[#1E3E62] text-white px-5 py-2 rounded-full font-medium flex items-center gap-1 hover:scale-105 transition-transform"
              data-aos="fade-up">
              Learn More</a>
          <span class="text-xl">➔</span>
          </button>
        </div>
      </div>


      <div class="grid md:grid-cols-2 gap-12 items-center" data-aos="fade-up">
        <!-- Left Image / Card -->
        <div class="bg-[#1E3E62] p-8 rounded-xl shadow-md flex justify-center items-center mb-6" data-aos="fade-up">
          <div class="bg-white p-6 rounded-lg shadow w-full max-w-md" data-aos="fade-up">
            <h4 class="text-sm font-semibold text-gray-700 mb-4">Task Manager</h4>
            <div class="space-y-2">
              <div class="flex items-center gap-2">
                <span class="w-3 h-3 bg-blue-800 rounded-full"></span>
                <div class="h-1 w-2/3 bg-gray-300 rounded"></div>
              </div>
              <div class="flex items-center gap-2">
                <span class="w-3 h-3 bg-pink-700 rounded-full"></span>
                <div class="h-1 w-1/2 bg-gray-300 rounded"></div>
              </div>
              <div class="flex items-center gap-2">
                <span class="w-3 h-3 bg-purple-700 rounded-full"></span>
                <div class="h-1 w-4/5 bg-gray-300 rounded"></div>
              </div>
              <div class="flex items-center gap-2">
                <span class="w-3 h-3 bg-cyan-500 rounded-full"></span>
                <div class="h-1 w-1/3 bg-gray-300 rounded"></div>
              </div>
            </div>
            <!-- Bar Graph Style -->
            <div class="mt-6 flex items-end justify-between gap-2">
              <div class="w-3 h-12 bg-purple-800 rounded"></div>
              <div class="w-3 h-8 bg-blue-400 rounded"></div>
              <div class="w-3 h-10 bg-cyan-400 rounded"></div>
              <div class="w-3 h-16 bg-indigo-800 rounded"></div>
              <div class="w-3 h-14 bg-pink-300 rounded"></div>
            </div>
          </div>
        </div>

        <!-- Right Text Content -->
        <div>
          <h3 class="text-2xl md:text-3xl font-bold mb-4" data-aos="fade-up">Task Manager</h3>
          <p class="text-gray-700 mb-6" data-aos="fade-up">
            Task Manager is a smart and simple productivity tool that helps individuals and teams organize tasks, set
            priorities, track progress, and collaborate in real-time. With features like deadlines, reminders, Kanban
            boards, and cross-device sync, it makes managing work easy, efficient, and seamless.
          </p>
          <a href="taskmanager.html"><button
              class="bg-[#1E3E62] text-white px-5 py-2 rounded-full font-medium flex items-center gap-1 hover:scale-105 transition-transform"
              data-aos="fade-up">
              Learn More</a>
          <span class="text-xl">➔</span>
          </button>
        </div>
      </div>
      <div class="grid md:grid-cols-2 gap-12 items-center" data-aos="fade-up">
        <!-- Left Image / Card -->
        <div class="bg-[#0B192C] p-8 rounded-xl shadow-md flex justify-center items-center mt-5" data-aos="fade-up">
          <div class="bg-white p-6 rounded-lg shadow w-full max-w-md" data-aos="fade-up">
            <h4 class="text-sm font-semibold text-gray-700 mb-4">Integration</h4>
            <div class="space-y-2">
              <div class="flex items-center gap-2">
                <span class="w-3 h-3 bg-blue-800 rounded-full"></span>
                <div class="h-1 w-2/3 bg-gray-300 rounded"></div>
              </div>
              <div class="flex items-center gap-2">
                <span class="w-3 h-3 bg-pink-700 rounded-full"></span>
                <div class="h-1 w-1/2 bg-gray-300 rounded"></div>
              </div>
              <div class="flex items-center gap-2">
                <span class="w-3 h-3 bg-purple-700 rounded-full"></span>
                <div class="h-1 w-4/5 bg-gray-300 rounded"></div>
              </div>
              <div class="flex items-center gap-2">
                <span class="w-3 h-3 bg-cyan-500 rounded-full"></span>
                <div class="h-1 w-1/3 bg-gray-300 rounded"></div>
              </div>
            </div>
            <!-- Bar Graph Style -->
            <div class="mt-6 flex items-end justify-between gap-2">
              <div class="w-3 h-12 bg-purple-800 rounded"></div>
              <div class="w-3 h-8 bg-blue-400 rounded"></div>
              <div class="w-3 h-10 bg-cyan-400 rounded"></div>
              <div class="w-3 h-16 bg-indigo-800 rounded"></div>
              <div class="w-3 h-14 bg-pink-300 rounded"></div>
            </div>
          </div>
        </div>

        <!-- Right Text Content -->
        <div>
          <h3 class="text-2xl md:text-3xl font-bold mb-4" data-aos="fade-up">Integration</h3>
          <p class="text-gray-700 mb-6" data-aos="fade-up">
            Our Task Manager seamlessly integrates with popular tools like Google Calendar, Slack, Microsoft Teams, and
            cloud storage platforms to streamline your workflow. These integrations ensure real-time updates,
            centralized communication, and easy access to files, helping teams stay connected and productive across
            platforms.
          </p>
          <a href="integration.html"><button
              class="bg-[#1E3E62] text-white px-5 py-2 rounded-full font-medium flex items-center gap-1 hover:scale-105 transition-transform"
              data-aos="fade-up">
              Learn More</a>
          <span class="text-xl">➔</span>
          </button>
        </div>
      </div>
  </section>

  

<!-- Why Choose Us Section -->
<section class="w-auto mx-auto px-4 sm:px-6 lg:px-8 mt-20 mb-20" data-aos="fade-up">
  <h1 class="text-4xl sm:text-5xl font-extrabold text-center text-transparent bg-gradient-to-r from-orange-500 to-blue-500 bg-clip-text mb-4">
    Why Choose Us
  </h1>
  <p class="text-base sm:text-lg font-medium text-center mb-2 text-gray-700">
    "Stay Ahead of Cyber Threats—Protect What Matters Most, Effortlessly."
  </p>
  <p class="text-base sm:text-lg text-center mb-12 text-gray-600">
    Get a free, personalized security assessment and discover how easy it is to secure your digital world.
  </p>

  <!-- Cards Section -->
  <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8 bg-gradient-to-br from-orange-50 to-blue-50 p-10 rounded-3xl shadow-xl" data-aos="fade-up">

    <!-- Card 1 -->
    <div class="bg-white rounded-2xl shadow-md p-6 transition-transform transform hover:-translate-y-2 hover:shadow-xl" data-aos="fade-up">
      <div class="flex items-center justify-center w-14 h-14 bg-orange-100 rounded-full mb-4">
        <span class="text-2xl">🛡️</span>
      </div>
      <h3 class="text-xl font-semibold mb-3 text-gray-800">Real-Time Protection</h3>
      <p class="text-gray-600 text-sm leading-relaxed">
        Our advanced security platform monitors your digital environment 24/7, instantly detecting and blocking threats before they can do harm.
      </p>
    </div>

    <!-- Card 2 -->
    <div class="bg-white rounded-2xl shadow-md p-6 transition-transform transform hover:-translate-y-2 hover:shadow-xl" data-aos="fade-up">
      <div class="flex items-center justify-center w-14 h-14 bg-blue-100 rounded-full mb-4">
        <span class="text-2xl">💼</span>
      </div>
      <h3 class="text-xl font-semibold mb-3 text-gray-800">Trusted by Industry Leaders</h3>
      <p class="text-gray-600 text-sm leading-relaxed">
        Join thousands of satisfied clients who rely on us to safeguard their data and peace of mind.
      </p>
    </div>

    <!-- Card 3 -->
    <div class="bg-white rounded-2xl shadow-md p-6 transition-transform transform hover:-translate-y-2 hover:shadow-xl">
      <div class="flex items-center justify-center w-14 h-14 bg-green-100 rounded-full mb-4">
        <span class="text-2xl">🧑‍💻</span>
      </div>
      <h3 class="text-xl font-semibold mb-3 text-gray-800">Effortless Experience</h3>
      <p class="text-gray-600 text-sm leading-relaxed">
        Enjoy intuitive interfaces and seamless onboarding—no technical expertise required.
      </p>
    </div>

    <!-- Card 4 -->
    <div class="bg-white rounded-2xl shadow-md p-6 transition-transform transform hover:-translate-y-2 hover:shadow-xl">
      <div class="flex items-center justify-center w-14 h-14 bg-purple-100 rounded-full mb-4">
        <span class="text-2xl">🚀</span>
      </div>
      <h3 class="text-xl font-semibold mb-3 text-gray-800">Fast Deployment</h3>
      <p class="text-gray-600 text-sm leading-relaxed">
        Get up and running in hours, not days—speed and simplicity combined for your success.
      </p>
    </div>

    <!-- Card 5 -->
    <div class="bg-white rounded-2xl shadow-md p-6 transition-transform transform hover:-translate-y-2 hover:shadow-xl" data-aos="fade-up">
      <div class="flex items-center justify-center w-14 h-14 bg-red-100 rounded-full mb-4">
        <span class="text-2xl">📊</span>
      </div>
      <h3 class="text-xl font-semibold mb-3 text-gray-800">Smart Analytics</h3>
      <p class="text-gray-600 text-sm leading-relaxed">
        Visualize security performance and risks in real time with intelligent dashboards and reporting tools.
      </p>
    </div>

    <!-- Card 6 -->
    <div class="bg-white rounded-2xl shadow-md p-6 transition-transform transform hover:-translate-y-2 hover:shadow-xl" data-aos="fade-up">
      <div class="flex items-center justify-center w-14 h-14 bg-yellow-100 rounded-full mb-4">
        <span class="text-2xl">🔒</span>
      </div>
      <h3 class="text-xl font-semibold mb-3 text-gray-800">Privacy First</h3>
      <p class="text-gray-600 text-sm leading-relaxed">
        We prioritize your data privacy with end-to-end encryption and GDPR-compliant infrastructure.
      </p>
    </div>

  </div>
</section>


  <section class="relative w-full min-h-[400px] sm:min-h-[450px] lg:min-h-[550px] flex items-center justify-center py-8 sm:py-10 lg:py-12">
  <div class="w-auto mx-auto px-4 sm:px-6 lg:px-20">
    <div
      class="bg-[#0B192C] rounded-tr-[60px] rounded-bl-[60px] w-full min-h-[350px] sm:min-h-[375px] lg:min-h-[400px] flex flex-col lg:flex-row items-center justify-between py-8 px-4 sm:px-8 lg:px-16 gap-8"
      data-aos="fade-up"
    >

      <!-- Left Text Content -->
      <div class="text-white flex flex-col justify-center w-full lg:w-1/2 text-start" data-aos="fade-up">
        <h1 class="text-2xl sm:text-3xl lg:text-[2.5rem] font-bold leading-tight mb-4">
          We solve people problems<br class="hidden sm:block" />
          and make them happy.
        </h1>
        <p class="text-[#d5ccf4] text-sm sm:text-base w-auto">
          As a full-service design agency, we work closely with our clients. Good people making great things. We help
          people to reshape their business.
        </p>
      </div>

      <!-- Right Image -->
      <div class="relative w-full lg:w-1/2 flex items-center justify-center" data-aos="fade-up">
        <div
          class="w-auto sm:w-[350px] lg:w-[600px] h-[200px] sm:h-[230px] lg:h-[250px] rounded-xl overflow-hidden shadow-lg"
        >
          <img src="images/officemeeting.jpg" alt="Office meeting" class="object-cover w-full h-full" />
        </div>
      </div>
    </div>
  </div>
</section>


<section class="py-16 px-4 sm:px-6 lg:px-12 bg-white" data-aos="fade-up">
  <div class="w-auto mx-auto flex flex-col lg:flex-row items-center gap-12">
    
    <!-- Left: Image -->
    <div class="relative w-full lg:w-1/2" data-aos="fade-up">
      <img src="images/index.webp" alt="Sales Report" class="rounded-3xl w-full h-[300px] sm:h-[350px] md:h-[400px] object-cover">
    </div>

    <!-- Right: Content and Stats -->
    <div class="w-full lg:w-1/2" data-aos="fade-up">
      <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold mb-4 text-center lg:text-left text-transparent bg-gradient-to-r from-orange-500 to-blue-500 bg-clip-text">
        Built for Real-Time Sales Teams.
      </h2>
      

      <p class="text-gray-600 mb-6 text-center lg:text-left">
        Every feature in our platform is designed with speed, visibility, and results in mind.
        From pipeline health to rep performance, see what's working — and what's not — instantly.
      </p>

      <!-- Features List -->
      <div class="grid grid-cols-2 sm:grid-cols-2 gap-x-4 gap-y-3 text-sm font-medium mb-8">
        <div class="flex items-center gap-2 text-gray-700">
          <span class="text-blue-500 text-lg">✔</span> Custom-Fit Engagement
        </div>
        <div class="flex items-center gap-2 text-gray-700">
          <span class="text-blue-500 text-lg">✔</span> Unified Integration
        </div>
        <div class="flex items-center gap-2 text-gray-700">
          <span class="text-blue-500 text-lg">✔</span> Insightful Analytics
        </div>
        <div class="flex items-center gap-2 text-gray-700">
          <span class="text-blue-500 text-lg">✔</span> Full-Time Customer Service
        </div>
      </div>

      <!-- Stats -->
      <div class="flex flex-col sm:flex-row justify-center lg:justify-start gap-4">
        <div class="bg-white shadow-md rounded-xl p-6 text-center w-full sm:w-1/3">
          <p class="text-2xl font-bold">140+</p>
          <p class="text-sm text-gray-600">Trusted Partners</p>
        </div>
        <div class="bg-white shadow-md rounded-xl p-6 text-center w-full sm:w-1/3">
          <p class="text-2xl font-bold">24K+</p>
          <p class="text-sm text-gray-600">Happy Clients</p>
        </div>
        <div class="bg-white shadow-md rounded-xl p-6 text-center w-full sm:w-1/3">
          <p class="text-2xl font-bold">8+</p>
          <p class="text-sm text-gray-600">A Legacy of Excellence</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Feature Comparison Table Section -->
<section class="py-16 px-4 sm:px-6 lg:px-12 bg-white">
  <div class="w-auto mx-auto text-center mb-12" data-aos="fade-up">
    <h2 class="text-4xl sm:text-5xl font-extrabold text-center text-transparent bg-gradient-to-r from-orange-500 to-blue-500 bg-clip-text mb-4"">Feature Comparison</h2>
    <p class="text-gray-600 text-lg">See how our LMS stacks up against the competition.</p>
  </div>
  <div class="overflow-x-auto" data-aos="fade-up">
    <table class="min-w-full bg-white rounded-xl shadow-lg">
      <thead>
        <tr>
          <th class="py-3 px-4 border-b text-left font-bold">Feature</th>
          <th class="py-3 px-4 border-b text-left font-bold">Our LMS</th>
          <th class="py-3 px-4 border-b text-left font-bold">Competitor A</th>
          <th class="py-3 px-4 border-b text-left font-bold">Competitor B</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td class="py-2 px-4 border-b">Lead Capture Automation</td>
          <td class="py-2 px-4 border-b text-green-600 font-bold">✔</td>
          <td class="py-2 px-4 border-b text-gray-400">✖</td>
          <td class="py-2 px-4 border-b text-gray-400">✖</td>
        </tr>
        <tr>
          <td class="py-2 px-4 border-b">Real-Time Analytics</td>
          <td class="py-2 px-4 border-b text-green-600 font-bold">✔</td>
          <td class="py-2 px-4 border-b text-green-600">✔</td>
          <td class="py-2 px-4 border-b text-gray-400">✖</td>
        </tr>
        <tr>
          <td class="py-2 px-4 border-b">Team Collaboration</td>
          <td class="py-2 px-4 border-b text-green-600 font-bold">✔</td>
          <td class="py-2 px-4 border-b text-gray-400">✖</td>
          <td class="py-2 px-4 border-b text-green-600">✔</td>
        </tr>
        <tr>
          <td class="py-2 px-4 border-b">Integration Support</td>
          <td class="py-2 px-4 border-b text-green-600 font-bold">✔</td>
          <td class="py-2 px-4 border-b text-green-600">✔</td>
          <td class="py-2 px-4 border-b text-gray-400">✖</td>
        </tr>
        <tr>
          <td class="py-2 px-4">GDPR Compliance</td>
          <td class="py-2 px-4 text-green-600 font-bold">✔</td>
          <td class="py-2 px-4 text-gray-400">✖</td>
          <td class="py-2 px-4 text-green-600">✔</td>
        </tr>
      </tbody>
    </table>
  </div>
</section>

<!-- Customer Testimonials Section -->
<section class="pt-4 sm:pt-6 lg:pt-8 pb-16 sm:pb-20 lg:pb-24 bg-white">
  <div class="w-full mx-auto text-center mb-12" data-aos="fade-up">
    <h2 class="text-4xl sm:text-5xl font-extrabold text-center text-transparent bg-gradient-to-r from-orange-500 to-blue-500 bg-clip-text mb-4">What Our Customers Say</h2>
    <p class="text-gray-600 text-lg">Real stories from real users who love our LMS.</p>
  </div>
  <div class="overflow-hidden relative w-full" data-aos="fade-up">
    <div class="flex testimonials-scroll space-x-8 items-center w-auto" data-aos="fade-up">
      <!-- Testimonial 1 -->
      <div class="bg-white rounded-xl shadow-lg p-6 flex flex-col items-center min-w-[320px] max-w-xs">
        <img src="images/customer.jpg" alt="Customer" class="w-16 h-16 rounded-full mb-4 object-cover">
        <p class="text-gray-700 italic mb-2">"The automation features have saved us hours every week! Highly recommended."</p>
        <span class="font-bold text-[#FF6500]">Aman S., Sales Manager</span>
      </div>
      <!-- Testimonial 2 -->
      <div class="bg-white rounded-xl shadow-lg p-6 flex flex-col items-center min-w-[320px] max-w-xs">
        <img src="images/lead.jpg" alt="Customer" class="w-16 h-16 rounded-full mb-4 object-cover">
        <p class="text-gray-700 italic mb-2">"Our team collaboration and lead tracking have never been smoother."</p>
        <span class="font-bold text-[#1E3E62]">Priya K., Team Lead</span>
      </div>
      <!-- Testimonial 3 -->
      <div class="bg-white rounded-xl shadow-lg p-6 flex flex-col items-center min-w-[320px] max-w-xs">
        <img src="images/products.jpg" alt="Customer" class="w-16 h-16 rounded-full mb-4 object-cover">
        <p class="text-gray-700 italic mb-2">"The analytics dashboard gives us instant insights. Love it!"</p>
        <span class="font-bold text-[#0B192C]">Rahul M., Product Head</span>
      </div>
      <!-- Testimonial 4 -->
      <div class="bg-white rounded-xl shadow-lg p-6 flex flex-col items-center min-w-[320px] max-w-xs">
        <img src="images/team.jpg" alt="Customer" class="w-16 h-16 rounded-full mb-4 object-cover">
        <p class="text-gray-700 italic mb-2">"Seamless integration and fantastic support. Our sales have grown!"</p>
        <span class="font-bold text-[#F7A041]">Sonia D., Operations</span>
      </div>
      <!-- Testimonial 5 -->
      <div class="bg-white rounded-xl shadow-lg p-6 flex flex-col items-center min-w-[320px] max-w-xs">
        <img src="images/officemeeting.jpg" alt="Customer" class="w-16 h-16 rounded-full mb-4 object-cover">
        <p class="text-gray-700 italic mb-2">"Easy to use and very effective for our growing business."</p>
        <span class="font-bold text-[#4A77A8]">Vikram P., Founder</span>
      </div>
      <!-- Testimonial 6 -->
      <div class="bg-white rounded-xl shadow-lg p-6 flex flex-col items-center min-w-[320px] max-w-xs">
        <img src="images/sales.jpg" alt="Customer" class="w-16 h-16 rounded-full mb-4 object-cover">
        <p class="text-gray-700 italic mb-2">"Customer support is top-notch. Highly responsive team!"</p>
        <span class="font-bold text-[#FF6500]">Meena R., Support Lead</span>
      </div>
      <!-- Repeat testimonials for infinite loop -->
      <div class="bg-white rounded-xl shadow-lg p-6 flex flex-col items-center min-w-[320px] max-w-xs">
        <img src="images/customer.jpg" alt="Customer" class="w-16 h-16 rounded-full mb-4 object-cover">
        <p class="text-gray-700 italic mb-2">"The automation features have saved us hours every week! Highly recommended."</p>
        <span class="font-bold text-[#FF6500]">Aman S., Sales Manager</span>
      </div>
      <div class="bg-white rounded-xl shadow-lg p-6 flex flex-col items-center min-w-[320px] max-w-xs">
        <img src="images/lead.jpg" alt="Customer" class="w-16 h-16 rounded-full mb-4 object-cover">
        <p class="text-gray-700 italic mb-2">"Our team collaboration and lead tracking have never been smoother."</p>
        <span class="font-bold text-[#1E3E62]">Priya K., Team Lead</span>
      </div>
      <div class="bg-white rounded-xl shadow-lg p-6 flex flex-col items-center min-w-[320px] max-w-xs">
        <img src="images/products.jpg" alt="Customer" class="w-16 h-16 rounded-full mb-4 object-cover">
        <p class="text-gray-700 italic mb-2">"The analytics dashboard gives us instant insights. Love it!"</p>
        <span class="font-bold text-[#0B192C]">Rahul M., Product Head</span>
      </div>
    </div>
  </div>
  <style>
    .testimonials-scroll {
      animation: testimonials-scroll 40s linear infinite;
    }
    @keyframes testimonials-scroll {
      0% { transform: translateX(0%); }
      100% { transform: translateX(-50%); }
    }
  </style>
</section>




 
<!-- Newsletter Section -->
<section class="pt-4 sm:pt-6 lg:pt-8 pb-16 sm:pb-20 lg:pb-24 bg-white">
  <div class="w-auto mx-auto flex flex-col md:flex-row items-center justify-center gap-10">
    
    <!-- Left Image -->
    <div class="w-full md:w-1/2 flex justify-center" data-aos="fade-up">
      <img
        src="images/alreadyacustomer.webp"
        alt="Man on phone"
        class="w-3/4 md:w-full max-w-xs md:max-w-sm lg:max-w-md object-contain"
      />
    </div>

    <!-- Right Content -->
    <div class="w-full md:w-1/2 bg-white rounded-2xl shadow-lg p-6 md:p-8 lg:p-10 mr-12" data-aos="fade-up">
      <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold mb-4 text-center md:text-left text-transparent bg-gradient-to-r from-orange-500 to-blue-500 bg-clip-text">
        Stay connected &amp; sign up for our newsletter
      </h2>
      <p class="text-base sm:text-lg text-gray-700 mb-6 text-center md:text-left">
        Join 10,000+ sales pros getting insider strategies, automation tips, and product updates.
        We'll send only what helps you close more, not clutter your inbox.
      </p>

      <!-- Form -->
      <form class="w-full flex flex-col sm:flex-row items-center space-y-3 sm:space-y-0 sm:space-x-3" data-aos="fade-up">
        <input
          type="email"
          placeholder="Enter your email"
          required
          class="flex-grow w-full sm:w-auto py-3 px-5 text-gray-700 rounded-full border border-gray-300 focus:border-[#FF6500] focus:outline-none"
        />
        <button
          type="submit"
          class="bg-[#FF6500] text-white px-6 py-3 rounded-full hover:bg-gray-900 transition w-full sm:w-auto"
        >
          Subscribe
        </button>
      </form>
    </div>
  </div>
</section>


<!-- Footer -->
<footer class="bg-black text-white px-6 sm:px-8 lg:px-12 w-full text-sm sm:text-base">
  <div class="w-full">
    <div class="py-16 grid grid-cols-1 md:grid-cols-4 gap-10 text-center md:text-left">
      <!-- Logo & Description -->
      <div class="flex flex-col items-center md:items-start space-y-6">
        <img src="images/logo.png" alt="Logo" class="w-40 sm:w-48 h-auto bg-white" />
        <p class="text-gray-400 text-sm sm:text-base max-w-sm text-center md:text-left">
          Effortless payroll, seamless success – simplify your pay process today.
        </p>
        <div class="flex gap-5 justify-center md:justify-start">
         <!-- Social Icons -->
         <a href="#" class="text-gray-400 hover:text-white transition">
          <!-- Facebook -->
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"/>
          </svg>
        </a>
        <!-- Twitter -->
        <a href="#" class="text-gray-400 hover:text-white transition">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"/>
          </svg>
        </a>
        <!-- Instagram -->
        <a href="#" class="text-gray-400 hover:text-white transition">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M7.75 2h8.5A5.75 5.75 0 0122 7.75v8.5A5.75 5.75 0 0116.25 22h-8.5A5.75 5.75 0 012 16.25v-8.5A5.75 5.75 0 017.75 2zm0 1.5A4.25 4.25 0 003.5 7.75v8.5A4.25 4.25 0 007.75 20.5h8.5a4.25 4.25 0 004.25-4.25v-8.5A4.25 4.25 0 0016.25 3.5h-8.5zm8.75 2a1 1 0 110 2 1 1 0 010-2zM12 7a5 5 0 110 10 5 5 0 010-10zm0 1.5a3.5 3.5 0 100 7 3.5 3.5 0 000-7z"/>
          </svg>
        </a>
        <!-- LinkedIn -->
        <a href="#" class="text-gray-400 hover:text-white transition">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M4.98 3.5C4.98 4.61 4.1 5.5 3 5.5S1.02 4.61 1.02 3.5 1.9 1.5 3 1.5 4.98 2.39 4.98 3.5zM.5 6h5V20h-5V6zm7.5 0h4.7v1.785h.066c.655-1.24 2.255-2.535 4.634-2.535 4.953 0 5.867 3.26 5.867 7.5V20h-5v-6.417c0-1.531-.027-3.5-2.134-3.5-2.138 0-2.466 1.668-2.466 3.39V20h-5V6z"/>
          </svg>
        </a>
        </div>
      </div>

      <!-- Quick Links -->
      <div class="space-y-4">
        <h3 class="text-base font-extrabold">Quick Links</h3>
        <ul class="space-y-2 text-gray-400 text-sm sm:text-base">
          <li><a href="index.html" class="hover:text-white">Home</a></li>
          <li><a href="about.html" class="hover:text-white">About Us</a></li>
          <li><a href="products.html" class="hover:text-white">Products</a></li>
          <li><a href="contact.html" class="hover:text-white">Contact Us</a></li>
          <li><a href="login.html" class="hover:text-white">Login</a></li>
        </ul>
      </div>

      <!-- Products -->
      <div class="space-y-4">
        <h3 class="text-base font-extrabold">Products</h3>
        <ul class="space-y-2 text-gray-400 text-sm sm:text-base">
          <li><a href="leadmanagement.html" class="hover:text-white">Lead Management</a></li>
          <li><a href="salemanagement.html" class="hover:text-white">Sales Management</a></li>
          <li><a href="analytics.html" class="hover:text-white">Advance Analytics</a></li>
          <li><a href="customersupport.html" class="hover:text-white">Customer Management</a></li>
          <li><a href="integration.html" class="hover:text-white">Integration</a></li>
        </ul>
      </div>

      <!-- Contact -->
      <div class="space-y-4">
        <h3 class="text-lg font-extrabold">Contact</h3>
        <ul class="space-y-3 text-gray-400 text-sm">
          <li class="flex items-center justify-center md:justify-start gap-2">
            <svg class="w-4 h-4" stroke="currentColor" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            <a href="mailto:<EMAIL>" class="hover:text-white"><EMAIL></a>
          </li>
          <li class="flex items-center justify-center md:justify-start gap-2">
            <svg class="w-4 h-4" stroke="currentColor" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
            </svg>
            +1 (555) 482-9316
          </li>
          <li class="flex items-start justify-center md:justify-start gap-2">
            <svg class="w-4 h-4" stroke="currentColor" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <span>
             Gurgaon, Haryana
            </span>
          </li>
        </ul>
      </div>
    </div>

    <!-- Bottom -->
    <div class="border-t border-gray-800 py-4 flex flex-col sm:flex-row justify-between items-center text-gray-400 text-sm sm:text-base gap-4 text-center">
      <p>© 2025 LMS. All rights reserved.</p>
      <div class="flex gap-4">
        <a href="#" class="hover:text-white">Terms & Conditions</a>
        <a href="#" class="hover:text-white">Privacy Policy</a>
      </div>
    </div>
  </div>
</footer>

  <style>
    /* Hamburger to X animation for mobile menu button */
    #mobileMenuBtn.open span:nth-child(1) {
      transform: rotate(45deg) translateY(8px);
    }
    #mobileMenuBtn.open span:nth-child(2) {
      opacity: 0;
    }
    #mobileMenuBtn.open span:nth-child(3) {
      transform: rotate(-45deg) translateY(-8px);
    }
  </style>

  <script src="https://unpkg.com/remixicon/fonts/remixicon.css"></script>
 
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: { primary: "#4A77A8", secondary: "#F7A041" },
          borderRadius: {
            none: "0px",
            sm: "4px",
            DEFAULT: "8px",
            md: "12px",
            lg: "16px",
            xl: "20px",
            "2xl": "24px",
            "3xl": "32px",
            full: "9999px",
            button: "8px",
          },
        },
      },
    };
  </script>
  <script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>
  <script>
    AOS.init();
  </script>

  <script>
    // Simple dropdown toggle for Features
    document.addEventListener('DOMContentLoaded', function () {
      const btn = document.getElementById('featuresDropdownBtn');
      const menu = document.getElementById('featuresDropdownMenu');
      btn.addEventListener('click', function (e) {
        e.stopPropagation();
        menu.classList.toggle('hidden');
      });
      // Close dropdown when clicking outside
      document.addEventListener('click', function (e) {
        if (!btn.contains(e.target) && !menu.contains(e.target)) {
          menu.classList.add('hidden');
        }
      });
    });
 
    // Mobile menu toggle
    document.addEventListener('DOMContentLoaded', function () {
      const mobileMenuBtn = document.getElementById('mobileMenuBtn');
      const mobileMenu = document.getElementById('mobileMenu');
      mobileMenuBtn.addEventListener('click', function (e) {
        e.stopPropagation();
        mobileMenu.classList.toggle('hidden');
        mobileMenuBtn.classList.toggle('open'); // Toggle open class for X animation
      });
      document.addEventListener('click', function (e) {
        if (!mobileMenu.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
          mobileMenu.classList.add('hidden');
          mobileMenuBtn.classList.remove('open'); // Remove open class if clicking outside
        }
      });

      // Mobile features dropdown
      const mobileFeaturesBtn = document.getElementById('mobileFeaturesBtn');
      const mobileFeaturesMenu = document.getElementById('mobileFeaturesMenu');
      mobileFeaturesBtn.addEventListener('click', function (e) {
        e.stopPropagation();
        mobileFeaturesMenu.classList.toggle('hidden');
      });
      document.addEventListener('click', function (e) {
        if (!mobileFeaturesMenu.contains(e.target) && !mobileFeaturesBtn.contains(e.target)) {
          mobileFeaturesMenu.classList.add('hidden');
        }
      });
    });
  </script>


</body>

</html>