<?php
// Get database connection
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';
ensure_session_started();
require_login();

// Set content type to JSON
header('Content-Type: application/json');

// Get the last time the user checked for leads
$last_check = isset($_SESSION['last_leads_check']) ? $_SESSION['last_leads_check'] : date('Y-m-d H:i:s', strtotime('-1 hour'));

// Update the last check time
$_SESSION['last_leads_check'] = date('Y-m-d H:i:s');

// Count new leads since last check
$sql = "SELECT COUNT(*) as new_leads FROM leads WHERE created_at > ?";
$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, "s", $last_check);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);
$row = mysqli_fetch_assoc($result);

// Return the count of new leads
echo json_encode([
    'success' => true,
    'newLeads' => intval($row['new_leads']),
    'lastCheck' => $last_check
]);
?>