<?php
// Get database connection
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';
ensure_session_started();
require_login();

// Set content type to JSON
header('Content-Type: application/json');

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    echo json_encode(['success' => false, 'message' => 'Lead ID is required']);
    exit;
}

$lead_id = intval($_GET['id']);

// Get lead details
$sql = "SELECT * FROM leads WHERE id = ?";
$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, "i", $lead_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) === 0) {
    echo json_encode(['success' => false, 'message' => 'Lead not found']);
    exit;
}

$lead = mysqli_fetch_assoc($result);

// Format the HTML for the lead details
$html = '
<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    <div>
        <h3 class="text-sm font-medium text-gray-500">Name</h3>
        <p class="text-base">' . htmlspecialchars($lead['first_name'] . ' ' . $lead['last_name']) . '</p>
    </div>
    <div>
        <h3 class="text-sm font-medium text-gray-500">Email</h3>
        <p class="text-base">' . htmlspecialchars($lead['email']) . '</p>
    </div>
    <div>
        <h3 class="text-sm font-medium text-gray-500">Phone</h3>
        <p class="text-base">' . htmlspecialchars($lead['phone']) . '</p>
    </div>
    <div>
        <h3 class="text-sm font-medium text-gray-500">Enquiry Type</h3>
        <p class="text-base">' . htmlspecialchars($lead['enquiry_type']) . '</p>
    </div>
    <div>
        <h3 class="text-sm font-medium text-gray-500">Status</h3>
        <p class="text-base">
            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ' . 
            (function($status) {
                switch ($status) {
                    case 'new': return 'bg-blue-100 text-blue-800';
                    case 'contacted': return 'bg-yellow-100 text-yellow-800';
                    case 'qualified': return 'bg-green-100 text-green-800';
                    case 'converted': return 'bg-purple-100 text-purple-800';
                    case 'closed': return 'bg-gray-100 text-gray-800';
                    default: return 'bg-gray-100 text-gray-800';
                }
            })($lead['status']) . '">
                ' . ucfirst(htmlspecialchars($lead['status'])) . '
            </span>
        </p>
    </div>
    <div>
        <h3 class="text-sm font-medium text-gray-500">Created</h3>
        <p class="text-base">' . date('M d, Y H:i', strtotime($lead['created_at'])) . '</p>
    </div>
</div>
<div class="mt-4">
    <h3 class="text-sm font-medium text-gray-500">Message</h3>
    <p class="text-base whitespace-pre-line">' . htmlspecialchars($lead['message']) . '</p>
</div>
<div class="mt-6 flex justify-between">
    <div>
        <a href="mailto:' . htmlspecialchars($lead['email']) . '" class="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition">
            <i class="fas fa-envelope mr-2"></i> Email Lead
        </a>
        <a href="tel:' . htmlspecialchars($lead['phone']) . '" class="inline-flex items-center px-4 py-2 bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition ml-2">
            <i class="fas fa-phone mr-2"></i> Call Lead
        </a>
    </div>
    <button type="button" onclick="openStatusModal(' . $lead['id'] . ', \'' . $lead['status'] . '\')" class="inline-flex items-center px-4 py-2 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 transition">
        <i class="fas fa-edit mr-2"></i> Update Status
    </button>
</div>
';

echo json_encode(['success' => true, 'html' => $html]);
?>